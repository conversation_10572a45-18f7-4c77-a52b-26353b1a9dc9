{"scenarios": {"eudr-suppliers": {"name": "EUDR Suppliers", "description": "Generate EUDR-compliant supplier data with deforestation risk assessments", "template": "eudr-suppliers.js", "topic": "eudr.suppliers.main.v1", "keyField": "supplierId"}, "eudr-products": {"name": "EUDR Products", "description": "Generate EUDR-regulated product data with commodity classifications", "template": "eudr-products.js", "topic": "eudr.products.main.v1", "keyField": "productId"}, "eudr-purchase-orders": {"name": "EUDR Purchase Orders", "description": "Generate purchase orders for EUDR-regulated commodities", "template": "eudr-purchase-orders.js", "topic": "eudr.purchase-orders.main.v1", "keyField": "purchaseOrderId"}, "eudr-inbound-shipments": {"name": "EUDR Inbound Shipments", "description": "Generate inbound shipment data with geolocation and traceability", "template": "eudr-inbound-shipments.js", "topic": "eudr.inbound-shipments.main.v1", "keyField": "shipmentId"}, "eudr-outbound-shipments": {"name": "EUDR Outbound Shipments", "description": "Generate outbound shipment data with compliance documentation", "template": "eudr-outbound-shipments.js", "topic": "eudr.outbound-shipments.main.v1", "keyField": "shipmentId"}, "eudr-assessment-status": {"name": "EUDR Assessment Status", "description": "Generate EUDR compliance assessment status updates", "template": "eudr-assessment-status.js", "topic": "eudr.assessment-status.main.v1", "keyField": "assessmentId"}}}