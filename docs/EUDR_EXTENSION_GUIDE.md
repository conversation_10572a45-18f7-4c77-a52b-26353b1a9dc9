# EUDR Scenario Extension Guide

This guide explains how to extend the EUDR Kafka Producer Tool with additional scenarios beyond the current EUDR Suppliers scenario.

## Current Status

The tool currently supports:
- ✅ **EUDR Suppliers** - Active and ready to use
- 🔧 **Additional scenarios** - Can be created following the established patterns

## Planned EUDR Scenarios

The following EUDR scenarios can be implemented using the established template patterns:

### 1. EUDR Products
- **Purpose**: Generate EUDR-regulated product data with commodity classifications
- **Key Features**: CN codes, risk assessments, supply chain traceability, geolocation data
- **Message Key**: `productId`
- **Suggested Topic**: `eudr.products.main.v1`
- **Template File**: `src/templates/eudr-products.js` (to be created)

### 2. EUDR Purchase Orders
- **Purpose**: Generate purchase orders for EUDR-regulated commodities
- **Key Features**: Multi-line items, compliance tracking, financial details, workflow status
- **Message Key**: `purchaseOrderId`
- **Suggested Topic**: `eudr.purchase-orders.main.v1`
- **Template File**: `src/templates/eudr-purchase-orders.js` (to be created)

### 3. EUDR Inbound Shipments
- **Purpose**: Generate inbound shipment data with tracking and compliance monitoring
- **Key Features**: Container tracking, customs clearance, route information, cargo details
- **Message Key**: `shipmentId`
- **Suggested Topic**: `eudr.inbound-shipments.main.v1`
- **Template File**: `src/templates/eudr-inbound-shipments.js` (to be created)

### 4. EUDR Outbound Shipments
- **Purpose**: Generate outbound shipment data with compliance documentation
- **Key Features**: Export documentation, EUDR statements, insurance, delivery tracking
- **Message Key**: `shipmentId`
- **Suggested Topic**: `eudr.outbound-shipments.main.v1`
- **Template File**: `src/templates/eudr-outbound-shipments.js` (to be created)

### 5. EUDR Assessment Status
- **Purpose**: Generate compliance assessment status updates
- **Key Features**: Risk levels, assessment outcomes, mitigation plans, audit trails
- **Message Key**: `assessmentId`
- **Suggested Topic**: `eudr.assessment-status.main.v1`
- **Template File**: `src/templates/eudr-assessment-status.js` (to be created)

## How to Add Additional Scenarios

### Step 1: Create the Template File

Create a new template file in `src/templates/` following the pattern of `eudr-suppliers.js`. See the "Creating New Custom Templates" section below for detailed guidance.

### Step 2: Update Scenarios Configuration

Edit `config/scenarios/scenarios.json` and add the desired scenario(s) to the `scenarios` object:

```json
{
  "scenarios": {
    "eudr-suppliers": {
      "name": "EUDR Suppliers",
      "description": "Generate EUDR-compliant supplier data with deforestation risk assessments",
      "template": "eudr-suppliers.js",
      "topic": "eudr.suppliers.main.v1",
      "keyField": "supplierId"
    },
    "eudr-products": {
      "name": "EUDR Products",
      "description": "Generate EUDR-regulated product data with commodity classifications",
      "template": "eudr-products.js",
      "topic": "eudr.products.main.v1",
      "keyField": "productId"
    }
  }
}
```

### Step 2: Test the Configuration

```bash
# List available scenarios
node src/index.js list-scenarios

# Test interactive mode
node src/index.js

# Test batch mode
node src/index.js batch --scenario eudr-products --environment dev --count 5
```

### Step 3: Verify Template Functionality

Each template includes comprehensive metadata and validation. Test a specific template:

```bash
# Test template loading
node -e "
const { TemplateEngine } = require('./src/utils/template-engine');
const engine = new TemplateEngine();
const template = engine.loadTemplate('eudr-products.js');
console.log('Template metadata:', template.getMetadata());
const message = template.generate({ messageIndex: 1 });
console.log('Sample message:', JSON.stringify(message, null, 2));
"
```

## Creating New Custom Templates

If you need scenarios beyond the pre-built ones, follow this template structure:

### Template File Structure

Create a new file in `src/templates/` (e.g., `my-custom-scenario.js`):

```javascript
const crypto = require("crypto");

// Helper functions
function generateRandomAlphanumeric(length) {
    return crypto.randomBytes(Math.ceil(length / 2)).toString("hex").slice(0, length);
}

function getRandomElement(array) {
    return array[Math.floor(Math.random() * array.length)];
}

/**
 * Generate a message for your custom scenario
 * @param {Object} options - Generation options
 * @returns {Object} Generated message with key and value
 */
function generate(options = {}) {
    const messageKey = `CUSTOM-${generateRandomAlphanumeric(8).toUpperCase()}`;
    
    const messageValue = {
        // Your custom message structure here
        id: messageKey,
        // ... other fields
        metadata: {
            generatedAt: new Date().toISOString(),
            messageIndex: options.messageIndex || 0,
            dataVersion: "1.0",
            source: "CUSTOM_SYSTEM"
        }
    };

    return {
        key: messageKey,
        value: messageValue
    };
}

/**
 * Get template metadata
 * @returns {Object} Template metadata
 */
function getMetadata() {
    return {
        name: "My Custom Scenario",
        description: "Description of what this template generates",
        version: "1.0.0",
        keyField: "id",
        author: "Your Name"
    };
}

module.exports = {
    generate,
    getMetadata
};
```

### Required Template Functions

Every template must export:

1. **`generate(options)`** - Main generation function
   - `options.messageIndex` - Current message index (for batch generation)
   - Returns `{ key: string, value: object }`

2. **`getMetadata()`** - Template information function
   - Returns metadata object with `name`, `description`, `version`, `keyField`, `author`

### Template Best Practices

1. **Consistent Key Generation**: Use a predictable prefix and random suffix
2. **Realistic Data**: Generate data that resembles real-world scenarios
3. **Metadata Fields**: Always include `generatedAt`, `messageIndex`, `dataVersion`, `source`
4. **Error Handling**: Handle edge cases gracefully
5. **Documentation**: Include JSDoc comments for functions

## EUDR-Specific Considerations

When creating EUDR-related templates, consider including:

### Core EUDR Fields
- **Commodity Types**: CATTLE, COCOA, COFFEE, PALM_OIL, RUBBER, SOY, WOOD
- **Risk Levels**: NEGLIGIBLE, LOW, STANDARD, HIGH, CRITICAL
- **Geolocation Data**: Coordinates, accuracy, collection method
- **Certifications**: FSC, PEFC, RTRS, RSPO, UTZ, RAINFOREST_ALLIANCE, etc.
- **Compliance Status**: COMPLIANT, NON_COMPLIANT, PENDING_REVIEW, etc.

### Data Relationships
- Link suppliers to products via `supplierId`
- Link products to purchase orders via `productId`
- Link purchase orders to shipments via `purchaseOrderId`
- Maintain consistent country codes and risk profiles

### Regulatory Compliance
- Include due diligence information
- Add traceability chain data
- Provide assessment and audit trails
- Include required documentation flags

## Testing Your Extensions

### Unit Testing Template
```bash
# Test template loading and generation
node -e "
const template = require('./src/templates/your-template.js');
console.log('Metadata:', template.getMetadata());
const msg = template.generate({ messageIndex: 1 });
console.log('Generated:', JSON.stringify(msg, null, 2));
"
```

### Integration Testing
```bash
# Test with the full application
node src/index.js batch --scenario your-scenario --environment dev --count 3 --show-payload
```

### Validation Checklist
- [ ] Template exports `generate` and `getMetadata` functions
- [ ] `generate()` returns `{ key, value }` structure
- [ ] Message keys are unique and consistent
- [ ] Metadata includes all required fields
- [ ] Template handles `options.messageIndex` parameter
- [ ] Generated data is realistic and varied
- [ ] No runtime errors during generation

## Troubleshooting

### Common Issues

1. **"Template not found"** - Check file exists in `src/templates/` and is referenced correctly in scenarios.json
2. **"Invalid template structure"** - Ensure template exports both required functions
3. **"Generation failed"** - Check for syntax errors and missing dependencies
4. **"Metadata validation failed"** - Verify all required metadata fields are present

### Debug Mode
```bash
# Enable debug logging
DEBUG=* node src/index.js batch --scenario your-scenario --count 1
```

## Support

For questions or issues with extending the EUDR scenarios:

1. Check the existing templates in `src/templates/` for examples
2. Review the template engine code in `src/utils/template-engine.js`
3. Test with small message counts first
4. Validate JSON structure before deploying to production

## Future Enhancements

Planned improvements for the extension system:
- Template validation CLI command
- Template generator wizard
- Schema validation for generated messages
- Performance benchmarking tools
- Template testing framework
