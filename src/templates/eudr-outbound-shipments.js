const crypto = require("crypto");

// --- Helper Functions ---
function generateRandomNumericString(length) {
    let result = "";
    const characters = "0123456789";
    for (let i = 0; i < length; i++) {
        result += characters.charAt(Math.floor(Math.random() * characters.length));
    }
    return result;
}

function generateRandomAlphanumeric(length) {
    return crypto
        .randomBytes(Math.ceil(length / 2))
        .toString("hex")
        .slice(0, length);
}

function getRandomElement(array) {
    return array[Math.floor(Math.random() * array.length)];
}

function getRandomFloat(min, max, decimals = 2) {
    return parseFloat((Math.random() * (max - min) + min).toFixed(decimals));
}

// --- EUDR Outbound Shipment Configuration ---

// Shipment statuses
const shipmentStatuses = [
    "PLANNED", "PREPARING", "READY_TO_SHIP", "PICKED_UP", "IN_TRANSIT", 
    "OUT_FOR_DELIVERY", "DELIVERED", "COMPLETED", "DELAYED", "CANCELLED"
];

// Transport modes
const transportModes = ["SEA_FREIGHT", "AIR_FREIGHT", "ROAD_TRANSPORT", "RAIL_TRANSPORT", "COURIER"];

// EUDR commodities
const eudrCommodities = ["CATTLE", "COCOA", "COFFEE", "PALM_OIL", "RUBBER", "SOY", "WOOD"];

// Destination regions
const destinationRegions = ["EU", "UK", "NORWAY", "SWITZERLAND"];

// Risk levels
const riskLevels = ["VERY_LOW", "LOW", "MEDIUM", "HIGH", "CRITICAL"];

// Compliance statuses
const complianceStatuses = ["COMPLIANT", "NON_COMPLIANT", "PENDING_REVIEW", "REQUIRES_DOCUMENTATION", "UNDER_ASSESSMENT"];

/**
 * Generate an EUDR outbound shipment message
 * @param {Object} options - Generation options
 * @returns {Object} Generated message with key and value
 */
function generate(options = {}) {
    // Generate shipment ID (our message key)
    const shipmentId = `EUDR-OB-${generateRandomAlphanumeric(10).toUpperCase()}`;
    
    // Generate shipment dates
    const shipmentDate = new Date();
    const pickupDate = new Date(shipmentDate.getTime() + Math.random() * 7 * 24 * 60 * 60 * 1000); // Within 7 days
    const estimatedDelivery = new Date(pickupDate.getTime() + (Math.random() * 21 + 1) * 24 * 60 * 60 * 1000); // 1-21 days transit
    
    // Generate transport details
    const transportMode = getRandomElement(transportModes);
    
    // Generate cargo details
    const cargoItems = generateCargoItems();
    const totalWeight = cargoItems.reduce((sum, item) => sum + item.weight, 0);
    const totalValue = cargoItems.reduce((sum, item) => sum + item.value, 0);
    
    // Generate destination information
    const destination = generateDestination();
    
    // Generate EUDR compliance information
    const overallRiskLevel = determineOverallRisk(cargoItems);
    const complianceScore = calculateComplianceScore(cargoItems);
    
    const messageValue = {
        shipmentId: shipmentId,
        shipmentInfo: {
            shipmentNumber: `OUT-${generateRandomNumericString(8)}`,
            status: getRandomElement(shipmentStatuses),
            priority: getRandomElement(["LOW", "NORMAL", "HIGH", "URGENT"]),
            shipmentType: "EUDR_REGULATED_EXPORT",
            shipmentDate: shipmentDate.toISOString().split('T')[0],
            pickupDate: pickupDate.toISOString().split('T')[0],
            estimatedDeliveryDate: estimatedDelivery.toISOString().split('T')[0],
            actualDeliveryDate: null // Will be updated when delivered
        },
        transport: {
            mode: transportMode,
            carrier: generateCarrierInfo(transportMode),
            service: generateServiceLevel(transportMode),
            trackingNumber: `TRK-${generateRandomAlphanumeric(12).toUpperCase()}`,
            estimatedTransitTime: Math.floor((estimatedDelivery - pickupDate) / (24 * 60 * 60 * 1000))
        },
        parties: {
            shipper: {
                id: `SHIPPER-${generateRandomAlphanumeric(8).toUpperCase()}`,
                name: generateCompanyName("shipper"),
                address: generateEUAddress(),
                contact: generateContact(),
                eudrRole: "OPERATOR"
            },
            consignee: {
                id: `CONSIGNEE-${generateRandomAlphanumeric(8).toUpperCase()}`,
                name: generateCompanyName("consignee"),
                address: destination.address,
                contact: generateContact(),
                eudrRole: getRandomElement(["OPERATOR", "TRADER"])
            }
        },
        destination: destination,
        cargo: {
            items: cargoItems,
            totalItems: cargoItems.length,
            totalWeight: parseFloat(totalWeight.toFixed(2)),
            totalValue: parseFloat(totalValue.toFixed(2)),
            currency: "EUR",
            weightUnit: "kg",
            packaging: generatePackagingInfo()
        },
        eudrCompliance: {
            overallRiskLevel: overallRiskLevel,
            complianceScore: complianceScore,
            assessmentStatus: getRandomElement(complianceStatuses),
            assessmentDate: new Date(shipmentDate.getTime() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            assessedBy: `ASSESSOR-${generateRandomAlphanumeric(6).toUpperCase()}`,
            eudrStatement: {
                provided: Math.random() > 0.05, // 95% have statement
                statementNumber: `EUDR-STMT-${generateRandomAlphanumeric(10).toUpperCase()}`,
                issueDate: new Date(shipmentDate.getTime() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                validUntil: new Date(shipmentDate.getTime() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
            },
            documentation: {
                dueDiligenceReport: Math.random() > 0.1, // 90% have report
                riskAssessment: Math.random() > 0.15, // 85% have assessment
                supplierDeclarations: Math.random() > 0.2, // 80% have declarations
                geolocationData: Math.random() > 0.25, // 75% have geolocation
                certifications: Math.random() > 0.3, // 70% have certifications
                chainOfCustody: Math.random() > 0.35 // 65% have chain of custody
            },
            alerts: generateComplianceAlerts(overallRiskLevel)
        },
        customs: {
            exportDeclaration: `EXP-${generateRandomNumericString(10)}`,
            customsValue: parseFloat(totalValue.toFixed(2)),
            currency: "EUR",
            exportLicense: Math.random() > 0.7 ? `LIC-${generateRandomAlphanumeric(8).toUpperCase()}` : null,
            clearanceStatus: getRandomElement(["PENDING", "CLEARED", "HELD", "EXAMINATION_REQUIRED"]),
            clearanceDate: Math.random() > 0.3 ? new Date(shipmentDate.getTime() + Math.random() * 3 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] : null
        },
        tracking: {
            currentStatus: getRandomElement(shipmentStatuses),
            currentLocation: generateCurrentLocation(),
            milestones: generateTrackingMilestones(shipmentDate, pickupDate, estimatedDelivery),
            lastUpdate: new Date().toISOString(),
            estimatedDelivery: estimatedDelivery.toISOString()
        },
        insurance: {
            insured: Math.random() > 0.2, // 80% insured
            insuranceValue: parseFloat((totalValue * 1.1).toFixed(2)), // 110% of cargo value
            currency: "EUR",
            policyNumber: `INS-${generateRandomAlphanumeric(10).toUpperCase()}`,
            provider: getRandomElement(["Lloyd's of London", "Allianz", "AXA", "Zurich", "Munich Re"])
        },
        metadata: {
            generatedAt: new Date().toISOString(),
            messageIndex: options.messageIndex || 0,
            dataVersion: "1.0",
            source: "EUDR_EXPORT_SYSTEM",
            lastUpdated: new Date().toISOString()
        }
    };

    return {
        key: shipmentId,
        value: messageValue
    };
}

// --- Helper Functions ---

function generateCargoItems() {
    const itemCount = Math.floor(Math.random() * 6) + 1; // 1-6 items
    const items = [];
    
    for (let i = 0; i < itemCount; i++) {
        const commodity = getRandomElement(eudrCommodities);
        const quantity = getRandomFloat(10, 1000);
        const weight = getRandomFloat(5, 500);
        const unitValue = getRandomFloat(10, 500);
        const totalValue = quantity * unitValue;
        
        items.push({
            itemNumber: i + 1,
            productId: `EUDR-PROD-${generateRandomAlphanumeric(10).toUpperCase()}`,
            commodity: commodity,
            productName: generateProductName(commodity),
            description: `Processed ${commodity.toLowerCase()} products`,
            hsCode: getHSCode(commodity),
            quantity: quantity,
            unit: getUnitForCommodity(commodity),
            weight: weight,
            unitValue: unitValue,
            value: parseFloat(totalValue.toFixed(2)),
            currency: "EUR",
            origin: generateOriginInfo(),
            eudrCompliance: {
                riskLevel: getRandomElement(riskLevels),
                complianceStatus: getRandomElement(complianceStatuses),
                certifications: generateCertifications(),
                geolocationVerified: Math.random() > 0.2, // 80% verified
                dueDiligenceCompleted: Math.random() > 0.15 // 85% completed
            },
            packaging: {
                type: getPackagingType(commodity),
                quantity: Math.floor(Math.random() * 50) + 1,
                weight: getRandomFloat(0.5, 5) // kg per package
            }
        });
    }
    
    return items;
}

function generateDestination() {
    const euCountries = [
        { code: "DE", name: "Germany", cities: ["Hamburg", "Munich", "Berlin", "Frankfurt"] },
        { code: "NL", name: "Netherlands", cities: ["Amsterdam", "Rotterdam", "The Hague", "Utrecht"] },
        { code: "FR", name: "France", cities: ["Paris", "Lyon", "Marseille", "Toulouse"] },
        { code: "BE", name: "Belgium", cities: ["Brussels", "Antwerp", "Ghent", "Liège"] },
        { code: "IT", name: "Italy", cities: ["Milan", "Rome", "Naples", "Turin"] },
        { code: "ES", name: "Spain", cities: ["Madrid", "Barcelona", "Valencia", "Seville"] }
    ];
    
    const country = getRandomElement(euCountries);
    const city = getRandomElement(country.cities);
    
    return {
        country: country.code,
        countryName: country.name,
        city: city,
        region: getRandomElement(destinationRegions),
        address: {
            street: `${Math.floor(Math.random() * 999) + 1} ${getRandomElement(["Industrial", "Business", "Commerce", "Trade"])} ${getRandomElement(["Street", "Avenue", "Boulevard"])}`,
            city: city,
            postalCode: generateRandomNumericString(5),
            country: country.name
        },
        port: generateNearestPort(country.code),
        coordinates: generateCoordinatesForCountry(country.code)
    };
}

function generateCarrierInfo(transportMode) {
    const carriers = {
        "SEA_FREIGHT": ["Maersk Line", "MSC", "CMA CGM", "COSCO", "Hapag-Lloyd"],
        "AIR_FREIGHT": ["Lufthansa Cargo", "Air France Cargo", "KLM Cargo", "British Airways Cargo"],
        "ROAD_TRANSPORT": ["DHL", "FedEx", "UPS", "TNT", "DB Schenker"],
        "RAIL_TRANSPORT": ["DB Cargo", "SNCF Connect", "Rail Cargo Group"],
        "COURIER": ["DHL Express", "FedEx Express", "UPS Express", "TNT Express"]
    };
    
    const carrierName = getRandomElement(carriers[transportMode] || carriers["ROAD_TRANSPORT"]);
    
    return {
        name: carrierName,
        code: generateRandomAlphanumeric(4).toUpperCase(),
        serviceType: generateServiceLevel(transportMode),
        contact: generateCarrierContact()
    };
}

function generateServiceLevel(transportMode) {
    const services = {
        "SEA_FREIGHT": ["FCL", "LCL", "Bulk"],
        "AIR_FREIGHT": ["Express", "Standard", "Economy"],
        "ROAD_TRANSPORT": ["Express", "Standard", "Economy"],
        "RAIL_TRANSPORT": ["Express", "Standard"],
        "COURIER": ["Next Day", "2-Day", "Standard"]
    };
    
    return getRandomElement(services[transportMode] || services["ROAD_TRANSPORT"]);
}

function generateCompanyName(type) {
    const prefixes = ["Global", "European", "Premium", "Sustainable", "Green", "Eco"];
    const suffixes = ["Corp", "Ltd", "Inc", "Group", "Trading", "Industries"];
    const types = {
        "shipper": ["Export", "Trading", "Distribution"],
        "consignee": ["Import", "Processing", "Manufacturing"]
    };
    
    return `${getRandomElement(prefixes)} ${getRandomElement(types[type] || ["Trading"])} ${getRandomElement(suffixes)}`;
}

function generateEUAddress() {
    const euCities = ["Amsterdam", "Hamburg", "Paris", "Brussels", "Milan", "Madrid"];
    const euCountries = ["Netherlands", "Germany", "France", "Belgium", "Italy", "Spain"];
    
    return {
        street: `${Math.floor(Math.random() * 999) + 1} Export Boulevard`,
        city: getRandomElement(euCities),
        postalCode: generateRandomNumericString(5),
        country: getRandomElement(euCountries)
    };
}

function generateContact() {
    const names = ["Hans Mueller", "Marie Dubois", "Giovanni Rossi", "Carlos Garcia", "Anna van der Berg"];
    const titles = ["Export Manager", "Logistics Coordinator", "Supply Chain Manager", "Operations Director"];
    
    return {
        name: getRandomElement(names),
        title: getRandomElement(titles),
        email: `<EMAIL>`,
        phone: `+${generateRandomNumericString(2)}${generateRandomNumericString(9)}`
    };
}

function generateCarrierContact() {
    return {
        customerService: `+${generateRandomNumericString(2)}${generateRandomNumericString(9)}`,
        email: "<EMAIL>",
        trackingUrl: "https://tracking.carrier.com"
    };
}

function generatePackagingInfo() {
    return {
        totalPackages: Math.floor(Math.random() * 100) + 1,
        packageTypes: getRandomElement([
            ["Boxes"], ["Pallets"], ["Containers"], 
            ["Boxes", "Pallets"], ["Pallets", "Containers"]
        ]),
        specialHandling: Math.random() > 0.7 ? getRandomElement([
            "Fragile", "Temperature Controlled", "Hazardous", "High Value"
        ]) : null
    };
}

function generateCurrentLocation() {
    const locations = [
        "Origin Warehouse", "Pickup Location", "Carrier Hub", 
        "In Transit", "Destination Hub", "Out for Delivery"
    ];
    
    return getRandomElement(locations);
}

function generateTrackingMilestones(shipmentDate, pickupDate, estimatedDelivery) {
    const milestones = [
        { event: "Shipment Created", date: shipmentDate.toISOString().split('T')[0], status: "COMPLETED" },
        { event: "Ready for Pickup", date: pickupDate.toISOString().split('T')[0], status: "PENDING" },
        { event: "Estimated Delivery", date: estimatedDelivery.toISOString().split('T')[0], status: "PENDING" }
    ];
    
    if (Math.random() > 0.5) {
        milestones.splice(1, 0, {
            event: "Documentation Verified",
            date: new Date(shipmentDate.getTime() + Math.random() * 2 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            status: "COMPLETED"
        });
    }
    
    return milestones;
}

function generateComplianceAlerts(riskLevel) {
    const alerts = [];
    
    if (riskLevel === "HIGH" || riskLevel === "CRITICAL") {
        alerts.push({
            type: "HIGH_RISK_EXPORT",
            message: "High-risk EUDR commodities require additional documentation",
            severity: "HIGH",
            timestamp: new Date().toISOString()
        });
    }
    
    if (Math.random() > 0.8) {
        alerts.push({
            type: "DOCUMENTATION_REVIEW",
            message: "EUDR documentation requires review before shipment",
            severity: "MEDIUM",
            timestamp: new Date().toISOString()
        });
    }
    
    return alerts;
}

function generateProductName(commodity) {
    const productNames = {
        "CATTLE": getRandomElement(["Processed Beef", "Beef Products", "Leather Goods"]),
        "COCOA": getRandomElement(["Chocolate Products", "Cocoa Powder", "Cocoa Butter"]),
        "COFFEE": getRandomElement(["Roasted Coffee", "Coffee Products", "Coffee Extract"]),
        "PALM_OIL": getRandomElement(["Refined Palm Oil", "Palm Oil Products", "Cosmetic Ingredients"]),
        "RUBBER": getRandomElement(["Rubber Products", "Latex Products", "Industrial Rubber"]),
        "SOY": getRandomElement(["Soy Products", "Soy Protein", "Food Ingredients"]),
        "WOOD": getRandomElement(["Furniture", "Wood Products", "Paper Products"])
    };
    
    return productNames[commodity] || "Processed Products";
}

function getHSCode(commodity) {
    const hsCodes = {
        "CATTLE": "0201",
        "COCOA": "1806",
        "COFFEE": "0901",
        "PALM_OIL": "1511",
        "RUBBER": "4001",
        "SOY": "1507",
        "WOOD": "4407"
    };
    
    return hsCodes[commodity] || "0000";
}

function getUnitForCommodity(commodity) {
    const units = {
        "CATTLE": "kg",
        "COCOA": "kg",
        "COFFEE": "kg",
        "PALM_OIL": "liters",
        "RUBBER": "kg",
        "SOY": "kg",
        "WOOD": "pieces"
    };
    
    return units[commodity] || "units";
}

function getPackagingType(commodity) {
    const packaging = {
        "CATTLE": "Vacuum Sealed",
        "COCOA": "Boxes",
        "COFFEE": "Bags",
        "PALM_OIL": "Containers",
        "RUBBER": "Bales",
        "SOY": "Bags",
        "WOOD": "Crates"
    };
    
    return packaging[commodity] || "Standard";
}

function generateOriginInfo() {
    return {
        facility: `FAC-${generateRandomAlphanumeric(8).toUpperCase()}`,
        location: "EU Processing Facility",
        country: getRandomElement(["NL", "DE", "FR", "BE", "IT"]),
        certified: Math.random() > 0.2 // 80% certified
    };
}

function generateCertifications() {
    const certs = ["FSC", "PEFC", "RTRS", "RSPO", "UTZ", "RAINFOREST_ALLIANCE", "ORGANIC"];
    const certCount = Math.floor(Math.random() * 3); // 0-2 certifications
    const selectedCerts = [];
    
    for (let i = 0; i < certCount; i++) {
        const cert = getRandomElement(certs);
        if (!selectedCerts.includes(cert)) {
            selectedCerts.push(cert);
        }
    }
    
    return selectedCerts;
}

function generateNearestPort(countryCode) {
    const ports = {
        "DE": "Hamburg",
        "NL": "Rotterdam",
        "FR": "Le Havre",
        "BE": "Antwerp",
        "IT": "Genoa",
        "ES": "Barcelona"
    };
    
    return ports[countryCode] || "Rotterdam";
}

function generateCoordinatesForCountry(countryCode) {
    const coords = {
        "DE": { lat: getRandomFloat(47.27, 55.06), lng: getRandomFloat(5.87, 15.04) },
        "NL": { lat: getRandomFloat(50.75, 53.55), lng: getRandomFloat(3.31, 7.09) },
        "FR": { lat: getRandomFloat(41.33, 51.12), lng: getRandomFloat(-5.14, 9.56) },
        "BE": { lat: getRandomFloat(49.50, 51.51), lng: getRandomFloat(2.54, 6.41) },
        "IT": { lat: getRandomFloat(35.49, 47.09), lng: getRandomFloat(6.63, 18.52) },
        "ES": { lat: getRandomFloat(35.17, 43.79), lng: getRandomFloat(-9.30, 4.32) }
    };
    
    return coords[countryCode] || { lat: 52.37, lng: 4.90 }; // Default to Amsterdam
}

function determineOverallRisk(cargoItems) {
    const riskScores = { "VERY_LOW": 1, "LOW": 2, "MEDIUM": 3, "HIGH": 4, "CRITICAL": 5 };
    const riskLevels = ["VERY_LOW", "LOW", "MEDIUM", "HIGH", "CRITICAL"];
    
    const avgRisk = cargoItems.reduce((sum, item) => sum + riskScores[item.eudrCompliance.riskLevel], 0) / cargoItems.length;
    
    return riskLevels[Math.round(avgRisk) - 1] || "MEDIUM";
}

function calculateComplianceScore(cargoItems) {
    const compliantItems = cargoItems.filter(item => item.eudrCompliance.complianceStatus === "COMPLIANT").length;
    return Math.round((compliantItems / cargoItems.length) * 100);
}

/**
 * Get template metadata
 * @returns {Object} Template metadata
 */
function getMetadata() {
    return {
        name: "EUDR Outbound Shipments Generator",
        description: "Generates outbound shipment data for EUDR-regulated commodities with compliance documentation and export tracking",
        version: "1.0.0",
        keyField: "shipmentId",
        author: "MuleSoft EUDR Team",
        supportedCommodities: eudrCommodities,
        transportModes: transportModes,
        destinationRegions: destinationRegions
    };
}

module.exports = {
    generate,
    getMetadata
};
