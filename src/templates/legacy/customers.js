const crypto = require("crypto");

// --- Helper Functions ---
function generateRandomNumericString(length) {
    let result = "";
    const characters = "0123456789";
    for (let i = 0; i < length; i++) {
        result += characters.charAt(Math.floor(Math.random() * characters.length));
    }
    return result;
}

function generateRandomAlphanumeric(length) {
    return crypto
        .randomBytes(Math.ceil(length / 2))
        .toString("hex")
        .slice(0, length);
}

function getRandomElement(array) {
    return array[Math.floor(Math.random() * array.length)];
}

function generateEmail(firstName, lastName) {
    const domains = ["gmail.com", "outlook.com", "yahoo.com", "company.com", "business.org"];
    const formats = [
        `${firstName.toLowerCase()}.${lastName.toLowerCase()}`,
        `${firstName.toLowerCase()}${lastName.toLowerCase()}`,
        `${firstName.charAt(0).toLowerCase()}${lastName.toLowerCase()}`,
        `${firstName.toLowerCase()}${generateRandomNumericString(2)}`
    ];
    
    return `${getRandomElement(formats)}@${getRandomElement(domains)}`;
}

// --- Configuration & Mappings ---

const firstNames = [
    "Emma", "Liam", "Olivia", "Noah", "Ava", "Ethan", "Sophia", "<PERSON>", "<PERSON>", "William",
    "Mia", "<PERSON>", "Charlotte", "Benjamin", "Amelia", "Lucas", "Harper", "Henry", "Evelyn", "Alexander",
    "Anna", "Max", "Lisa", "Tom", "Sarah", "David", "Maria", "John", "Elena", "Michael"
];

const lastNames = [
    "Smith", "Johnson", "Williams", "Brown", "Jones", "Garcia", "Miller", "Davis", "Rodriguez", "Martinez",
    "Hernandez", "Lopez", "Gonzalez", "Wilson", "Anderson", "Thomas", "Taylor", "Moore", "Jackson", "Martin",
    "Lee", "Perez", "Thompson", "White", "Harris", "Sanchez", "Clark", "Ramirez", "Lewis", "Robinson"
];

const customerTypes = ["INDIVIDUAL", "BUSINESS", "PREMIUM", "CORPORATE"];
const customerStatuses = ["ACTIVE", "INACTIVE", "SUSPENDED", "PENDING_VERIFICATION"];
const preferredChannels = ["EMAIL", "SMS", "PHONE", "MAIL", "MOBILE_APP"];
const countries = ["US", "CA", "GB", "DE", "FR", "IT", "ES", "NL", "BE", "AU"];

const cities = {
    "US": ["New York", "Los Angeles", "Chicago", "Houston", "Phoenix"],
    "CA": ["Toronto", "Vancouver", "Montreal", "Calgary", "Ottawa"],
    "GB": ["London", "Manchester", "Birmingham", "Liverpool", "Leeds"],
    "DE": ["Berlin", "Munich", "Hamburg", "Cologne", "Frankfurt"],
    "FR": ["Paris", "Lyon", "Marseille", "Toulouse", "Nice"],
    "IT": ["Rome", "Milan", "Naples", "Turin", "Palermo"],
    "ES": ["Madrid", "Barcelona", "Valencia", "Seville", "Bilbao"],
    "NL": ["Amsterdam", "Rotterdam", "The Hague", "Utrecht", "Eindhoven"],
    "BE": ["Brussels", "Antwerp", "Ghent", "Charleroi", "Liège"],
    "AU": ["Sydney", "Melbourne", "Brisbane", "Perth", "Adelaide"]
};

/**
 * Generate a customer profile message
 * @param {Object} options - Generation options
 * @returns {Object} Generated message with key and value
 */
function generate(options = {}) {
    // Generate customer ID (our message key)
    const customerId = `CUST-${generateRandomNumericString(8)}`;
    
    // Generate personal information
    const firstName = getRandomElement(firstNames);
    const lastName = getRandomElement(lastNames);
    const country = getRandomElement(countries);
    const city = getRandomElement(cities[country]);
    
    // Generate dates
    const registrationDate = new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000 * 3); // Up to 3 years ago
    const lastLoginDate = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000); // Up to 30 days ago
    
    // Generate preferences
    const preferences = {
        newsletter: Math.random() > 0.3,
        promotions: Math.random() > 0.4,
        smsNotifications: Math.random() > 0.6,
        preferredChannel: getRandomElement(preferredChannels),
        language: country === "DE" ? "de" : country === "FR" ? "fr" : country === "ES" ? "es" : country === "IT" ? "it" : country === "NL" ? "nl" : "en"
    };
    
    // Generate loyalty information
    const loyaltyPoints = Math.floor(Math.random() * 10000);
    const loyaltyTier = loyaltyPoints > 5000 ? "GOLD" : loyaltyPoints > 2000 ? "SILVER" : "BRONZE";
    
    const messageValue = {
        customerId: customerId,
        customerNumber: `CN${generateRandomNumericString(10)}`,
        type: getRandomElement(customerTypes),
        status: getRandomElement(customerStatuses),
        personalInfo: {
            firstName: firstName,
            lastName: lastName,
            fullName: `${firstName} ${lastName}`,
            email: generateEmail(firstName, lastName),
            phone: `+${country === "US" ? "1" : country === "CA" ? "1" : "44"}${generateRandomNumericString(10)}`,
            dateOfBirth: new Date(1950 + Math.random() * 50, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1).toISOString().split('T')[0]
        },
        address: {
            street: `${Math.floor(Math.random() * 9999) + 1} ${getRandomElement(["Main", "Oak", "Pine", "Elm", "Maple", "Cedar", "Park", "First", "Second", "Third"])} ${getRandomElement(["St", "Ave", "Blvd", "Dr", "Ln"])}`,
            city: city,
            state: country === "US" ? getRandomElement(["CA", "NY", "TX", "FL", "IL"]) : null,
            postalCode: generateRandomNumericString(5),
            country: country
        },
        account: {
            registrationDate: registrationDate.toISOString().split('T')[0],
            lastLoginDate: lastLoginDate.toISOString(),
            isVerified: Math.random() > 0.1, // 90% verified
            accountBalance: parseFloat((Math.random() * 1000).toFixed(2)),
            currency: country === "GB" ? "GBP" : country === "US" || country === "CA" ? "USD" : "EUR"
        },
        preferences: preferences,
        loyalty: {
            points: loyaltyPoints,
            tier: loyaltyTier,
            memberSince: registrationDate.toISOString().split('T')[0],
            totalSpent: parseFloat((Math.random() * 50000).toFixed(2))
        },
        segments: [
            getRandomElement(["HIGH_VALUE", "FREQUENT_BUYER", "OCCASIONAL", "NEW_CUSTOMER"]),
            getRandomElement(["MOBILE_USER", "WEB_USER", "STORE_VISITOR"]),
            getRandomElement(["PRICE_SENSITIVE", "BRAND_LOYAL", "DEAL_SEEKER"])
        ].slice(0, Math.floor(Math.random() * 3) + 1), // 1-3 segments
        metadata: {
            generatedAt: new Date().toISOString(),
            messageIndex: options.messageIndex || 0,
            source: getRandomElement(["WEB_REGISTRATION", "MOBILE_APP", "IN_STORE", "REFERRAL", "SOCIAL_MEDIA"]),
            lastUpdated: new Date().toISOString()
        }
    };

    return {
        key: customerId,
        value: messageValue
    };
}

/**
 * Get template metadata
 * @returns {Object} Template metadata
 */
function getMetadata() {
    return {
        name: "Customer Profile Generator",
        description: "Generates realistic customer profile test data with personal information, preferences, loyalty data, and segmentation",
        version: "1.0.0",
        keyField: "customerId",
        author: "MuleSoft Team",
        supportedCountries: countries,
        customerTypes: customerTypes
    };
}

module.exports = {
    generate,
    getMetadata
};
