const crypto = require("crypto");

// --- Helper Functions ---
function generateRandomNumericString(length) {
    let result = "";
    const characters = "0123456789";
    for (let i = 0; i < length; i++) {
        result += characters.charAt(Math.floor(Math.random() * characters.length));
    }
    return result;
}

function generateRandomAlphanumeric(length) {
    return crypto
        .randomBytes(Math.ceil(length / 2))
        .toString("hex")
        .slice(0, length);
}

function getRandomElement(array) {
    return array[Math.floor(Math.random() * array.length)];
}

function getRandomFloat(min, max, decimals = 2) {
    return parseFloat((Math.random() * (max - min) + min).toFixed(decimals));
}

// --- EUDR Inbound Shipment Configuration ---

// Shipment statuses
const shipmentStatuses = [
    "PLANNED", "BOOKED", "IN_TRANSIT", "CUSTOMS_CLEARANCE", "ARRIVED", 
    "UNLOADING", "INSPECTION", "DELIVERED", "COMPLETED", "DELAYED", "CANCELLED"
];

// Transport modes
const transportModes = ["SEA_FREIGHT", "AIR_FREIGHT", "ROAD_TRANSPORT", "RAIL_TRANSPORT", "MULTIMODAL"];

// EUDR commodities
const eudrCommodities = ["CATTLE", "COCOA", "COFFEE", "PALM_OIL", "RUBBER", "SOY", "WOOD"];

// Container types
const containerTypes = ["20FT_DRY", "40FT_DRY", "40FT_HC", "20FT_REEFER", "40FT_REEFER", "BULK", "BREAK_BULK"];

// Document types
const documentTypes = [
    "BILL_OF_LADING", "COMMERCIAL_INVOICE", "PACKING_LIST", "CERTIFICATE_OF_ORIGIN",
    "PHYTOSANITARY_CERTIFICATE", "EUDR_STATEMENT", "SUPPLIER_DECLARATION", 
    "RISK_ASSESSMENT", "GEOLOCATION_DATA", "CERTIFICATION"
];

// Risk levels
const riskLevels = ["VERY_LOW", "LOW", "MEDIUM", "HIGH", "CRITICAL"];

// Compliance statuses
const complianceStatuses = ["COMPLIANT", "NON_COMPLIANT", "PENDING_REVIEW", "REQUIRES_DOCUMENTATION", "UNDER_ASSESSMENT"];

/**
 * Generate an EUDR inbound shipment message
 * @param {Object} options - Generation options
 * @returns {Object} Generated message with key and value
 */
function generate(options = {}) {
    // Generate shipment ID (our message key)
    const shipmentId = `EUDR-IB-${generateRandomAlphanumeric(10).toUpperCase()}`;
    
    // Generate shipment dates
    const shipmentDate = new Date(Date.now() - Math.random() * 60 * 24 * 60 * 60 * 1000); // Up to 60 days ago
    const estimatedArrival = new Date(shipmentDate.getTime() + (Math.random() * 45 + 5) * 24 * 60 * 60 * 1000); // 5-50 days transit
    const actualArrival = Math.random() > 0.3 ? 
        new Date(estimatedArrival.getTime() + (Math.random() * 10 - 5) * 24 * 60 * 60 * 1000) : null; // ±5 days variation
    
    // Generate transport details
    const transportMode = getRandomElement(transportModes);
    const vesselInfo = generateVesselInfo(transportMode);
    
    // Generate cargo details
    const cargoItems = generateCargoItems();
    const totalWeight = cargoItems.reduce((sum, item) => sum + item.weight, 0);
    const totalVolume = cargoItems.reduce((sum, item) => sum + item.volume, 0);
    
    // Generate route information
    const route = generateRoute();
    
    // Generate EUDR compliance information
    const overallRiskLevel = determineOverallRisk(cargoItems);
    const complianceScore = calculateComplianceScore(cargoItems);
    
    const messageValue = {
        shipmentId: shipmentId,
        shipmentInfo: {
            shipmentNumber: `SH-${generateRandomNumericString(8)}`,
            status: getRandomElement(shipmentStatuses),
            priority: getRandomElement(["LOW", "NORMAL", "HIGH", "URGENT"]),
            shipmentType: "EUDR_REGULATED_IMPORT",
            shipmentDate: shipmentDate.toISOString().split('T')[0],
            estimatedArrivalDate: estimatedArrival.toISOString().split('T')[0],
            actualArrivalDate: actualArrival ? actualArrival.toISOString().split('T')[0] : null
        },
        transport: {
            mode: transportMode,
            vessel: vesselInfo,
            containerInfo: generateContainerInfo(),
            route: route,
            transitTime: {
                estimated: Math.floor((estimatedArrival - shipmentDate) / (24 * 60 * 60 * 1000)),
                actual: actualArrival ? Math.floor((actualArrival - shipmentDate) / (24 * 60 * 60 * 1000)) : null
            }
        },
        parties: {
            shipper: {
                id: `SHIPPER-${generateRandomAlphanumeric(8).toUpperCase()}`,
                name: generateCompanyName("shipper"),
                address: generateOriginAddress(),
                contact: generateContact()
            },
            consignee: {
                id: `CONSIGNEE-${generateRandomAlphanumeric(8).toUpperCase()}`,
                name: generateCompanyName("consignee"),
                address: generateDestinationAddress(),
                contact: generateContact()
            },
            carrier: {
                id: `CARRIER-${generateRandomAlphanumeric(6).toUpperCase()}`,
                name: generateCarrierName(transportMode),
                scac: generateRandomAlphanumeric(4).toUpperCase()
            }
        },
        cargo: {
            items: cargoItems,
            totalItems: cargoItems.length,
            totalWeight: parseFloat(totalWeight.toFixed(2)),
            totalVolume: parseFloat(totalVolume.toFixed(2)),
            weightUnit: "tonnes",
            volumeUnit: "cubic_meters"
        },
        eudrCompliance: {
            overallRiskLevel: overallRiskLevel,
            complianceScore: complianceScore,
            assessmentStatus: getRandomElement(complianceStatuses),
            assessmentDate: new Date(shipmentDate.getTime() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            assessedBy: `ASSESSOR-${generateRandomAlphanumeric(6).toUpperCase()}`,
            requiresInspection: Math.random() > 0.4, // 60% require inspection
            inspectionStatus: Math.random() > 0.5 ? getRandomElement(["SCHEDULED", "IN_PROGRESS", "COMPLETED", "PASSED", "FAILED"]) : null,
            documentation: {
                eudrStatement: Math.random() > 0.1, // 90% have statement
                supplierDeclaration: Math.random() > 0.15, // 85% have declaration
                riskAssessment: Math.random() > 0.2, // 80% have risk assessment
                geolocationData: Math.random() > 0.25, // 75% have geolocation
                certifications: Math.random() > 0.3, // 70% have certifications
                customsDeclaration: Math.random() > 0.05 // 95% have customs declaration
            },
            alerts: generateComplianceAlerts(overallRiskLevel)
        },
        tracking: {
            currentLocation: generateCurrentLocation(route, shipmentStatuses.indexOf(getRandomElement(shipmentStatuses))),
            milestones: generateTrackingMilestones(shipmentDate, estimatedArrival),
            lastUpdate: new Date().toISOString(),
            trackingNumber: `TRK-${generateRandomAlphanumeric(12).toUpperCase()}`
        },
        customs: {
            portOfEntry: route.destination.port,
            customsReference: `CUST-${generateRandomNumericString(10)}`,
            declarationNumber: `DECL-${generateRandomNumericString(8)}`,
            clearanceStatus: getRandomElement(["PENDING", "IN_PROGRESS", "CLEARED", "HELD", "EXAMINATION_REQUIRED"]),
            dutyPaid: Math.random() > 0.3,
            taxesOwed: getRandomFloat(1000, 50000),
            currency: "EUR"
        },
        documents: generateDocuments(),
        metadata: {
            generatedAt: new Date().toISOString(),
            messageIndex: options.messageIndex || 0,
            dataVersion: "1.0",
            source: "EUDR_LOGISTICS_SYSTEM",
            lastUpdated: new Date().toISOString()
        }
    };

    return {
        key: shipmentId,
        value: messageValue
    };
}

// --- Helper Functions ---

function generateVesselInfo(transportMode) {
    if (transportMode === "SEA_FREIGHT") {
        return {
            name: `MV ${getRandomElement(["Atlantic", "Pacific", "Global", "Ocean", "Maritime"])} ${getRandomElement(["Star", "Pioneer", "Explorer", "Trader", "Navigator"])}`,
            imo: `IMO${generateRandomNumericString(7)}`,
            flag: getRandomElement(["Liberia", "Panama", "Marshall Islands", "Singapore", "Malta"]),
            operator: generateCarrierName("SEA_FREIGHT")
        };
    } else if (transportMode === "AIR_FREIGHT") {
        return {
            flightNumber: `${getRandomElement(["LH", "AF", "KL", "BA", "LX"])}${generateRandomNumericString(4)}`,
            aircraft: getRandomElement(["Boeing 747F", "Airbus A330F", "Boeing 777F", "MD-11F"]),
            airline: generateCarrierName("AIR_FREIGHT")
        };
    } else {
        return {
            vehicleId: `VEH-${generateRandomAlphanumeric(8).toUpperCase()}`,
            operator: generateCarrierName(transportMode)
        };
    }
}

function generateContainerInfo() {
    const containerCount = Math.floor(Math.random() * 5) + 1; // 1-5 containers
    const containers = [];
    
    for (let i = 0; i < containerCount; i++) {
        containers.push({
            containerNumber: `${getRandomElement(["MSCU", "GESU", "TEMU", "HLXU"])}${generateRandomNumericString(7)}`,
            type: getRandomElement(containerTypes),
            sealNumber: `SEAL-${generateRandomNumericString(6)}`,
            weight: getRandomFloat(5, 30), // tonnes
            tareWeight: getRandomFloat(2, 4) // tonnes
        });
    }
    
    return containers;
}

function generateCargoItems() {
    const itemCount = Math.floor(Math.random() * 8) + 1; // 1-8 items
    const items = [];
    
    for (let i = 0; i < itemCount; i++) {
        const commodity = getRandomElement(eudrCommodities);
        const quantity = getRandomFloat(100, 5000);
        const weight = getRandomFloat(50, 1000);
        const volume = getRandomFloat(10, 200);
        
        items.push({
            itemNumber: i + 1,
            productId: `EUDR-PROD-${generateRandomAlphanumeric(10).toUpperCase()}`,
            commodity: commodity,
            description: `${commodity.toLowerCase()} products`,
            hsCode: getHSCode(commodity),
            quantity: quantity,
            unit: getUnitForCommodity(commodity),
            weight: weight,
            volume: volume,
            packaging: getPackagingType(commodity),
            origin: generateOriginInfo(),
            eudrCompliance: {
                riskLevel: getRandomElement(riskLevels),
                complianceStatus: getRandomElement(complianceStatuses),
                certifications: generateCertifications(),
                geolocationVerified: Math.random() > 0.3 // 70% verified
            }
        });
    }
    
    return items;
}

function generateRoute() {
    const origins = [
        { port: "Santos, Brazil", country: "BR", coordinates: { lat: -23.9608, lng: -46.3331 } },
        { port: "Jakarta, Indonesia", country: "ID", coordinates: { lat: -6.2088, lng: 106.8456 } },
        { port: "Port Klang, Malaysia", country: "MY", coordinates: { lat: 3.0044, lng: 101.3974 } },
        { port: "Cartagena, Colombia", country: "CO", coordinates: { lat: 10.3910, lng: -75.4794 } },
        { port: "Tema, Ghana", country: "GH", coordinates: { lat: 5.6698, lng: -0.0166 } }
    ];
    
    const destinations = [
        { port: "Rotterdam, Netherlands", country: "NL", coordinates: { lat: 51.9244, lng: 4.4777 } },
        { port: "Hamburg, Germany", country: "DE", coordinates: { lat: 53.5511, lng: 9.9937 } },
        { port: "Le Havre, France", country: "FR", coordinates: { lat: 49.4944, lng: 0.1079 } },
        { port: "Antwerp, Belgium", country: "BE", coordinates: { lat: 51.2194, lng: 4.4025 } }
    ];
    
    return {
        origin: getRandomElement(origins),
        destination: getRandomElement(destinations),
        transitPorts: generateTransitPorts()
    };
}

function generateTransitPorts() {
    const transitPorts = [
        "Algeciras, Spain", "Gibraltar", "Malta", "Suez Canal", "Singapore", "Dubai, UAE"
    ];
    
    const portCount = Math.floor(Math.random() * 3); // 0-2 transit ports
    const selectedPorts = [];
    
    for (let i = 0; i < portCount; i++) {
        const port = getRandomElement(transitPorts);
        if (!selectedPorts.includes(port)) {
            selectedPorts.push(port);
        }
    }
    
    return selectedPorts;
}

function generateOriginInfo() {
    const countries = ["BR", "ID", "MY", "CO", "PE", "GH", "CI", "VN", "TH"];
    const country = getRandomElement(countries);
    
    return {
        country: country,
        region: generateRegionForCountry(country),
        coordinates: generateCoordinatesForCountry(country),
        producer: `PRODUCER-${generateRandomAlphanumeric(8).toUpperCase()}`
    };
}

function generateCompanyName(type) {
    const prefixes = ["Global", "International", "Premium", "Sustainable", "Green"];
    const suffixes = ["Corp", "Ltd", "Inc", "Group", "Trading", "Logistics"];
    const types = {
        "shipper": ["Export", "Supply", "Trading"],
        "consignee": ["Import", "Distribution", "Processing"],
        "carrier": ["Shipping", "Logistics", "Transport"]
    };
    
    return `${getRandomElement(prefixes)} ${getRandomElement(types[type] || ["Trading"])} ${getRandomElement(suffixes)}`;
}

function generateCarrierName(transportMode) {
    const carriers = {
        "SEA_FREIGHT": ["Maersk Line", "MSC", "CMA CGM", "COSCO", "Hapag-Lloyd"],
        "AIR_FREIGHT": ["Lufthansa Cargo", "Air France Cargo", "KLM Cargo", "British Airways Cargo"],
        "ROAD_TRANSPORT": ["DHL", "FedEx", "UPS", "TNT", "DB Schenker"],
        "RAIL_TRANSPORT": ["DB Cargo", "SNCF Connect", "Rail Cargo Group"]
    };
    
    return getRandomElement(carriers[transportMode] || carriers["SEA_FREIGHT"]);
}

function generateOriginAddress() {
    return {
        street: `${Math.floor(Math.random() * 999) + 1} Export Boulevard`,
        city: getRandomElement(["Santos", "Jakarta", "Kuala Lumpur", "Cartagena", "Tema"]),
        country: getRandomElement(["Brazil", "Indonesia", "Malaysia", "Colombia", "Ghana"])
    };
}

function generateDestinationAddress() {
    return {
        street: `${Math.floor(Math.random() * 999) + 1} Import Street`,
        city: getRandomElement(["Rotterdam", "Hamburg", "Le Havre", "Antwerp", "Amsterdam"]),
        country: getRandomElement(["Netherlands", "Germany", "France", "Belgium"])
    };
}

function generateContact() {
    const names = ["John Smith", "Maria Silva", "Ahmed Rahman", "Anna Mueller", "Carlos Garcia"];
    const titles = ["Logistics Manager", "Import/Export Manager", "Supply Chain Coordinator", "Operations Manager"];
    
    return {
        name: getRandomElement(names),
        title: getRandomElement(titles),
        email: `<EMAIL>`,
        phone: `+${generateRandomNumericString(2)}${generateRandomNumericString(9)}`
    };
}

function generateCurrentLocation(route, statusIndex) {
    const locations = [
        route.origin.port,
        ...route.transitPorts,
        route.destination.port
    ];
    
    const locationIndex = Math.min(statusIndex / 3, locations.length - 1);
    return locations[Math.floor(locationIndex)] || route.origin.port;
}

function generateTrackingMilestones(shipmentDate, estimatedArrival) {
    const milestones = [
        { event: "Shipment Booked", date: shipmentDate.toISOString().split('T')[0], status: "COMPLETED" },
        { event: "Cargo Loaded", date: new Date(shipmentDate.getTime() + 24 * 60 * 60 * 1000).toISOString().split('T')[0], status: "COMPLETED" },
        { event: "Departed Origin Port", date: new Date(shipmentDate.getTime() + 2 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], status: "COMPLETED" }
    ];
    
    if (Math.random() > 0.5) {
        milestones.push({
            event: "In Transit",
            date: new Date(shipmentDate.getTime() + Math.random() * 20 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            status: "IN_PROGRESS"
        });
    }
    
    milestones.push({
        event: "Estimated Arrival",
        date: estimatedArrival.toISOString().split('T')[0],
        status: "PENDING"
    });
    
    return milestones;
}

function generateDocuments() {
    const documents = [];
    const docCount = Math.floor(Math.random() * 6) + 4; // 4-9 documents
    
    for (let i = 0; i < docCount; i++) {
        const docType = getRandomElement(documentTypes);
        documents.push({
            type: docType,
            number: `${docType.substring(0, 3)}-${generateRandomAlphanumeric(8).toUpperCase()}`,
            issueDate: new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            isValid: Math.random() > 0.05, // 95% valid
            verified: Math.random() > 0.1 // 90% verified
        });
    }
    
    return documents;
}

function generateComplianceAlerts(riskLevel) {
    const alerts = [];
    
    if (riskLevel === "HIGH" || riskLevel === "CRITICAL") {
        alerts.push({
            type: "HIGH_RISK_SHIPMENT",
            message: "Shipment contains high-risk EUDR commodities requiring enhanced due diligence",
            severity: "HIGH",
            timestamp: new Date().toISOString()
        });
    }
    
    if (Math.random() > 0.7) {
        alerts.push({
            type: "DOCUMENTATION_INCOMPLETE",
            message: "Missing required EUDR documentation",
            severity: "MEDIUM",
            timestamp: new Date().toISOString()
        });
    }
    
    return alerts;
}

function getHSCode(commodity) {
    const hsCodes = {
        "CATTLE": "0102",
        "COCOA": "1801",
        "COFFEE": "0901",
        "PALM_OIL": "1511",
        "RUBBER": "4001",
        "SOY": "1201",
        "WOOD": "4403"
    };
    
    return hsCodes[commodity] || "0000";
}

function getUnitForCommodity(commodity) {
    const units = {
        "CATTLE": "heads",
        "COCOA": "tonnes",
        "COFFEE": "tonnes",
        "PALM_OIL": "tonnes",
        "RUBBER": "tonnes",
        "SOY": "tonnes",
        "WOOD": "cubic_meters"
    };
    
    return units[commodity] || "units";
}

function getPackagingType(commodity) {
    const packaging = {
        "CATTLE": "Live Transport",
        "COCOA": "Jute Bags",
        "COFFEE": "Jute Bags",
        "PALM_OIL": "Bulk Tank",
        "RUBBER": "Bales",
        "SOY": "Bulk Container",
        "WOOD": "Bundle"
    };
    
    return packaging[commodity] || "Standard";
}

function generateCertifications() {
    const certs = ["FSC", "PEFC", "RTRS", "RSPO", "UTZ", "RAINFOREST_ALLIANCE"];
    const certCount = Math.floor(Math.random() * 3); // 0-2 certifications
    const selectedCerts = [];
    
    for (let i = 0; i < certCount; i++) {
        const cert = getRandomElement(certs);
        if (!selectedCerts.includes(cert)) {
            selectedCerts.push(cert);
        }
    }
    
    return selectedCerts;
}

function generateRegionForCountry(countryCode) {
    const regions = {
        "BR": getRandomElement(["Amazon", "Cerrado", "Atlantic Forest"]),
        "ID": getRandomElement(["Sumatra", "Java", "Kalimantan"]),
        "MY": getRandomElement(["Peninsular Malaysia", "Sabah", "Sarawak"]),
        "CO": getRandomElement(["Amazon", "Andes", "Caribbean"]),
        "PE": getRandomElement(["Amazon", "Andes", "Coast"]),
        "GH": getRandomElement(["Northern", "Middle Belt", "Forest Zone"]),
        "CI": getRandomElement(["Forest Zone", "Savanna", "Coastal"]),
        "VN": getRandomElement(["North", "Central", "South"]),
        "TH": getRandomElement(["North", "Northeast", "Central", "South"])
    };
    
    return regions[countryCode] || "Unknown Region";
}

function generateCoordinatesForCountry(countryCode) {
    const coords = {
        "BR": { lat: getRandomFloat(-33.75, 5.27), lng: getRandomFloat(-73.99, -34.79) },
        "ID": { lat: getRandomFloat(-10.36, 5.90), lng: getRandomFloat(95.01, 141.03) },
        "MY": { lat: getRandomFloat(0.85, 7.36), lng: getRandomFloat(99.64, 119.27) },
        "CO": { lat: getRandomFloat(-4.23, 12.44), lng: getRandomFloat(-81.73, -66.87) },
        "PE": { lat: getRandomFloat(-18.35, -0.04), lng: getRandomFloat(-81.33, -68.65) },
        "GH": { lat: getRandomFloat(4.74, 11.17), lng: getRandomFloat(-3.26, 1.19) },
        "CI": { lat: getRandomFloat(4.36, 10.74), lng: getRandomFloat(-8.60, -2.49) },
        "VN": { lat: getRandomFloat(8.18, 23.39), lng: getRandomFloat(102.14, 109.46) },
        "TH": { lat: getRandomFloat(5.61, 20.46), lng: getRandomFloat(97.34, 105.64) }
    };
    
    return coords[countryCode] || { lat: 0, lng: 0 };
}

function determineOverallRisk(cargoItems) {
    const riskScores = { "VERY_LOW": 1, "LOW": 2, "MEDIUM": 3, "HIGH": 4, "CRITICAL": 5 };
    const riskLevels = ["VERY_LOW", "LOW", "MEDIUM", "HIGH", "CRITICAL"];
    
    const avgRisk = cargoItems.reduce((sum, item) => sum + riskScores[item.eudrCompliance.riskLevel], 0) / cargoItems.length;
    
    return riskLevels[Math.round(avgRisk) - 1] || "MEDIUM";
}

function calculateComplianceScore(cargoItems) {
    const compliantItems = cargoItems.filter(item => item.eudrCompliance.complianceStatus === "COMPLIANT").length;
    return Math.round((compliantItems / cargoItems.length) * 100);
}

/**
 * Get template metadata
 * @returns {Object} Template metadata
 */
function getMetadata() {
    return {
        name: "EUDR Inbound Shipments Generator",
        description: "Generates inbound shipment data for EUDR-regulated commodities with tracking, compliance monitoring, and geolocation information",
        version: "1.0.0",
        keyField: "shipmentId",
        author: "MuleSoft EUDR Team",
        supportedCommodities: eudrCommodities,
        transportModes: transportModes,
        shipmentStatuses: shipmentStatuses
    };
}

module.exports = {
    generate,
    getMetadata
};
