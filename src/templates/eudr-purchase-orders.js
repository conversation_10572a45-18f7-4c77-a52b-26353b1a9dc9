const crypto = require("crypto");

// --- Helper Functions ---
function generateRandomNumericString(length) {
    let result = "";
    const characters = "0123456789";
    for (let i = 0; i < length; i++) {
        result += characters.charAt(Math.floor(Math.random() * characters.length));
    }
    return result;
}

function generateRandomAlphanumeric(length) {
    return crypto
        .randomBytes(Math.ceil(length / 2))
        .toString("hex")
        .slice(0, length);
}

function getRandomElement(array) {
    return array[Math.floor(Math.random() * array.length)];
}

function getRandomFloat(min, max, decimals = 2) {
    return parseFloat((Math.random() * (max - min) + min).toFixed(decimals));
}

// --- EUDR Purchase Order Configuration ---

// EUDR-regulated commodities
const eudrCommodities = ["CATTLE", "COCOA", "COFFEE", "PALM_OIL", "RUBBER", "SOY", "WOOD"];

// Order statuses
const orderStatuses = ["DRAFT", "PENDING_APPROVAL", "APPROVED", "SENT", "ACKNOWLEDGED", "IN_PROGRESS", "SHIPPED", "DELIVERED", "COMPLETED", "CANCELLED"];

// Payment terms
const paymentTerms = ["NET30", "NET60", "NET90", "COD", "PREPAID", "LC", "DOCUMENTARY_CREDIT"];

// Incoterms
const incoterms = ["EXW", "FCA", "CPT", "CIP", "DAP", "DPU", "DDP", "FAS", "FOB", "CFR", "CIF"];

// Currencies
const currencies = ["USD", "EUR", "GBP", "BRL", "IDR", "MYR", "COP", "PEN", "GHS", "XOF", "VND", "THB"];

// Risk levels
const riskLevels = ["VERY_LOW", "LOW", "MEDIUM", "HIGH", "CRITICAL"];

// Compliance statuses
const complianceStatuses = ["COMPLIANT", "NON_COMPLIANT", "PENDING_REVIEW", "REQUIRES_DOCUMENTATION", "UNDER_ASSESSMENT"];

/**
 * Generate an EUDR purchase order message
 * @param {Object} options - Generation options
 * @returns {Object} Generated message with key and value
 */
function generate(options = {}) {
    // Generate purchase order ID (our message key)
    const purchaseOrderId = `EUDR-PO-${generateRandomAlphanumeric(10).toUpperCase()}`;
    
    // Generate order details
    const orderDate = new Date();
    const requestedDeliveryDate = new Date(orderDate.getTime() + (Math.random() * 90 + 7) * 24 * 60 * 60 * 1000); // 7-97 days from now
    const expectedDeliveryDate = new Date(requestedDeliveryDate.getTime() + (Math.random() * 14) * 24 * 60 * 60 * 1000); // Up to 2 weeks later
    
    // Generate buyer and supplier information
    const buyerId = `BUYER-${generateRandomAlphanumeric(8).toUpperCase()}`;
    const supplierId = `EUDR-SUP-${generateRandomAlphanumeric(8).toUpperCase()}`;
    
    // Generate line items (1-5 items per order)
    const itemCount = Math.floor(Math.random() * 5) + 1;
    const lineItems = [];
    let totalOrderValue = 0;
    
    for (let i = 0; i < itemCount; i++) {
        const commodity = getRandomElement(eudrCommodities);
        const quantity = getRandomFloat(100, 10000);
        const unitPrice = getRandomFloat(0.5, 50);
        const lineTotal = quantity * unitPrice;
        totalOrderValue += lineTotal;
        
        const lineItem = {
            lineNumber: i + 1,
            productId: `EUDR-PROD-${generateRandomAlphanumeric(10).toUpperCase()}`,
            commodity: commodity,
            productName: generateProductName(commodity),
            description: `EUDR-regulated ${commodity.toLowerCase()} product`,
            cnCode: getCNCode(commodity),
            quantity: {
                ordered: quantity,
                unit: getUnitForCommodity(commodity)
            },
            pricing: {
                unitPrice: unitPrice,
                currency: getRandomElement(currencies),
                lineTotal: parseFloat(lineTotal.toFixed(2))
            },
            eudrCompliance: {
                riskLevel: getRandomElement(riskLevels),
                complianceStatus: getRandomElement(complianceStatuses),
                requiresDueDiligence: Math.random() > 0.2, // 80% require due diligence
                certificationRequired: Math.random() > 0.3, // 70% require certification
                geolocationRequired: Math.random() > 0.25, // 75% require geolocation
                traceabilityLevel: getRandomElement(["FULL", "PARTIAL", "LIMITED"])
            },
            specifications: {
                grade: getRandomElement(["A", "B", "C"]),
                quality: getRandomElement(["Premium", "Standard", "Commercial"]),
                moistureContent: getRandomFloat(5, 15),
                purity: getRandomFloat(85, 99.5),
                packaging: getPackagingType(commodity)
            },
            delivery: {
                requestedDate: requestedDeliveryDate.toISOString().split('T')[0],
                location: generateDeliveryLocation(),
                incoterm: getRandomElement(incoterms)
            }
        };
        
        lineItems.push(lineItem);
    }
    
    // Calculate totals
    const currency = getRandomElement(currencies);
    const taxRate = 0.20; // 20% VAT
    const taxAmount = parseFloat((totalOrderValue * taxRate).toFixed(2));
    const finalTotal = parseFloat((totalOrderValue + taxAmount).toFixed(2));
    
    // Generate EUDR compliance summary
    const overallRiskLevel = determineOverallRisk(lineItems);
    const complianceScore = calculateComplianceScore(lineItems);
    
    const messageValue = {
        purchaseOrderId: purchaseOrderId,
        orderInfo: {
            orderNumber: `PO-${generateRandomNumericString(8)}`,
            orderDate: orderDate.toISOString().split('T')[0],
            status: getRandomElement(orderStatuses),
            priority: getRandomElement(["LOW", "NORMAL", "HIGH", "URGENT"]),
            orderType: "EUDR_REGULATED_COMMODITIES",
            requestedDeliveryDate: requestedDeliveryDate.toISOString().split('T')[0],
            expectedDeliveryDate: expectedDeliveryDate.toISOString().split('T')[0]
        },
        parties: {
            buyer: {
                id: buyerId,
                name: generateCompanyName("buyer"),
                address: generateAddress("buyer"),
                contact: generateContact(),
                eudrRole: "OPERATOR"
            },
            supplier: {
                id: supplierId,
                name: generateCompanyName("supplier"),
                address: generateAddress("supplier"),
                contact: generateContact(),
                eudrRole: getRandomElement(["OPERATOR", "TRADER"])
            }
        },
        lineItems: lineItems,
        financial: {
            currency: currency,
            subtotal: parseFloat(totalOrderValue.toFixed(2)),
            taxRate: taxRate,
            taxAmount: taxAmount,
            totalAmount: finalTotal,
            paymentTerms: getRandomElement(paymentTerms),
            paymentMethod: getRandomElement(["BANK_TRANSFER", "LETTER_OF_CREDIT", "DOCUMENTARY_COLLECTION", "CASH_IN_ADVANCE"])
        },
        shipping: {
            incoterm: getRandomElement(incoterms),
            shippingMethod: getRandomElement(["SEA_FREIGHT", "AIR_FREIGHT", "ROAD_TRANSPORT", "RAIL_TRANSPORT"]),
            portOfLoading: generatePort("loading"),
            portOfDischarge: generatePort("discharge"),
            estimatedTransitTime: Math.floor(Math.random() * 30) + 5 // 5-35 days
        },
        eudrCompliance: {
            overallRiskLevel: overallRiskLevel,
            complianceScore: complianceScore,
            requiresEudrStatement: Math.random() > 0.1, // 90% require EUDR statement
            dueDiligenceRequired: Math.random() > 0.15, // 85% require due diligence
            assessmentStatus: getRandomElement(complianceStatuses),
            assessmentDate: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            nextReviewDate: new Date(Date.now() + Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            complianceOfficer: `OFFICER-${generateRandomAlphanumeric(6).toUpperCase()}`,
            documentation: {
                eudrStatement: Math.random() > 0.2, // 80% have statement
                supplierDeclaration: Math.random() > 0.15, // 85% have declaration
                riskAssessment: Math.random() > 0.25, // 75% have risk assessment
                geolocationData: Math.random() > 0.3, // 70% have geolocation
                certifications: Math.random() > 0.4 // 60% have certifications
            }
        },
        workflow: {
            createdBy: `USER-${generateRandomAlphanumeric(6).toUpperCase()}`,
            approvedBy: Math.random() > 0.3 ? `APPROVER-${generateRandomAlphanumeric(6).toUpperCase()}` : null,
            approvalDate: Math.random() > 0.3 ? new Date(orderDate.getTime() + Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] : null,
            sentDate: Math.random() > 0.5 ? new Date(orderDate.getTime() + Math.random() * 14 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] : null,
            acknowledgedDate: Math.random() > 0.6 ? new Date(orderDate.getTime() + Math.random() * 21 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] : null
        },
        metadata: {
            generatedAt: new Date().toISOString(),
            messageIndex: options.messageIndex || 0,
            dataVersion: "1.0",
            source: "EUDR_PROCUREMENT_SYSTEM",
            lastUpdated: new Date().toISOString()
        }
    };

    return {
        key: purchaseOrderId,
        value: messageValue
    };
}

// --- Helper Functions ---

function generateProductName(commodity) {
    const productNames = {
        "CATTLE": getRandomElement(["Live Cattle", "Fresh Beef", "Frozen Beef", "Beef Products"]),
        "COCOA": getRandomElement(["Cocoa Beans", "Cocoa Paste", "Cocoa Butter", "Cocoa Powder"]),
        "COFFEE": getRandomElement(["Green Coffee Beans", "Roasted Coffee", "Coffee Extract"]),
        "PALM_OIL": getRandomElement(["Crude Palm Oil", "Refined Palm Oil", "Palm Kernel Oil"]),
        "RUBBER": getRandomElement(["Natural Rubber", "Latex", "Rubber Sheets"]),
        "SOY": getRandomElement(["Soybeans", "Soy Oil", "Soy Meal"]),
        "WOOD": getRandomElement(["Logs", "Sawn Wood", "Plywood", "Timber"])
    };
    
    return productNames[commodity] || "Unknown Product";
}

function getCNCode(commodity) {
    const cnCodes = {
        "CATTLE": getRandomElement(["0102", "0201", "0202"]),
        "COCOA": getRandomElement(["1801", "1803", "1804", "1805"]),
        "COFFEE": "0901",
        "PALM_OIL": getRandomElement(["1511", "1513"]),
        "RUBBER": "4001",
        "SOY": getRandomElement(["1201", "1507", "2304"]),
        "WOOD": getRandomElement(["4403", "4407", "4409"])
    };
    
    return cnCodes[commodity] || "0000";
}

function getUnitForCommodity(commodity) {
    const units = {
        "CATTLE": "heads",
        "COCOA": "tonnes",
        "COFFEE": "tonnes",
        "PALM_OIL": "tonnes",
        "RUBBER": "tonnes",
        "SOY": "tonnes",
        "WOOD": "cubic_meters"
    };
    
    return units[commodity] || "units";
}

function getPackagingType(commodity) {
    const packaging = {
        "CATTLE": "Live Transport",
        "COCOA": "Jute Bags",
        "COFFEE": "Jute Bags",
        "PALM_OIL": "Bulk Tank",
        "RUBBER": "Bales",
        "SOY": "Bulk Container",
        "WOOD": "Bundle"
    };
    
    return packaging[commodity] || "Standard Packaging";
}

function generateCompanyName(type) {
    const prefixes = ["Global", "International", "Premium", "Sustainable", "Green", "Eco", "Natural"];
    const suffixes = ["Corp", "Ltd", "Inc", "Group", "Trading", "Industries", "Partners", "Solutions"];
    const types = type === "buyer" ? ["Procurement", "Import", "Trading"] : ["Export", "Supply", "Production"];
    
    return `${getRandomElement(prefixes)} ${getRandomElement(types)} ${getRandomElement(suffixes)}`;
}

function generateAddress(type) {
    const countries = type === "buyer" ? 
        ["Netherlands", "Germany", "France", "Belgium", "Italy"] : 
        ["Brazil", "Indonesia", "Malaysia", "Colombia", "Ghana"];
    
    return {
        street: `${Math.floor(Math.random() * 999) + 1} ${getRandomElement(["Business", "Industrial", "Trade", "Commerce"])} ${getRandomElement(["Street", "Avenue", "Boulevard"])}`,
        city: getRandomElement(["Amsterdam", "Hamburg", "Paris", "Brussels", "Milan"]),
        postalCode: generateRandomNumericString(5),
        country: getRandomElement(countries)
    };
}

function generateContact() {
    const firstNames = ["John", "Maria", "Ahmed", "Anna", "Carlos", "Lisa", "David", "Sarah"];
    const lastNames = ["Smith", "Silva", "Rahman", "Mueller", "Garcia", "Johnson", "Brown", "Davis"];
    
    const firstName = getRandomElement(firstNames);
    const lastName = getRandomElement(lastNames);
    
    return {
        name: `${firstName} ${lastName}`,
        title: getRandomElement(["Procurement Manager", "Supply Chain Director", "Trading Manager", "Operations Manager"]),
        email: `${firstName.toLowerCase()}.${lastName.toLowerCase()}@company.com`,
        phone: `+${generateRandomNumericString(2)}${generateRandomNumericString(9)}`
    };
}

function generateDeliveryLocation() {
    return {
        name: `${getRandomElement(["Central", "North", "South", "East", "West"])} Distribution Center`,
        address: generateAddress("buyer"),
        coordinates: {
            lat: getRandomFloat(40, 60),
            lng: getRandomFloat(-10, 30)
        }
    };
}

function generatePort(type) {
    const loadingPorts = ["Santos (Brazil)", "Jakarta (Indonesia)", "Port Klang (Malaysia)", "Cartagena (Colombia)", "Tema (Ghana)"];
    const dischargePorts = ["Rotterdam (Netherlands)", "Hamburg (Germany)", "Le Havre (France)", "Antwerp (Belgium)", "Genoa (Italy)"];
    
    return type === "loading" ? getRandomElement(loadingPorts) : getRandomElement(dischargePorts);
}

function determineOverallRisk(lineItems) {
    const riskScores = { "VERY_LOW": 1, "LOW": 2, "MEDIUM": 3, "HIGH": 4, "CRITICAL": 5 };
    const riskLevels = ["VERY_LOW", "LOW", "MEDIUM", "HIGH", "CRITICAL"];
    
    const avgRisk = lineItems.reduce((sum, item) => sum + riskScores[item.eudrCompliance.riskLevel], 0) / lineItems.length;
    
    return riskLevels[Math.round(avgRisk) - 1] || "MEDIUM";
}

function calculateComplianceScore(lineItems) {
    const compliantItems = lineItems.filter(item => item.eudrCompliance.complianceStatus === "COMPLIANT").length;
    return Math.round((compliantItems / lineItems.length) * 100);
}

/**
 * Get template metadata
 * @returns {Object} Template metadata
 */
function getMetadata() {
    return {
        name: "EUDR Purchase Orders Generator",
        description: "Generates purchase orders for EUDR-regulated commodities with compliance tracking and risk assessment",
        version: "1.0.0",
        keyField: "purchaseOrderId",
        author: "MuleSoft EUDR Team",
        supportedCommodities: eudrCommodities,
        orderStatuses: orderStatuses,
        riskLevels: riskLevels
    };
}

module.exports = {
    generate,
    getMetadata
};
