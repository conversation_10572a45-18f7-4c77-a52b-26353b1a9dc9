const crypto = require("crypto");

// --- Helper Functions ---
function generateRandomNumericString(length) {
    let result = "";
    const characters = "0123456789";
    for (let i = 0; i < length; i++) {
        result += characters.charAt(Math.floor(Math.random() * characters.length));
    }
    return result;
}

function generateRandomAlphanumeric(length) {
    return crypto
        .randomBytes(Math.ceil(length / 2))
        .toString("hex")
        .slice(0, length);
}

function getRandomElement(array) {
    return array[Math.floor(Math.random() * array.length)];
}

function getRandomFloat(min, max, decimals = 2) {
    return parseFloat((Math.random() * (max - min) + min).toFixed(decimals));
}

// --- EUDR Assessment Status Configuration ---

// Assessment types
const assessmentTypes = [
    "INITIAL_ASSESSMENT", "PERIODIC_REVIEW", "TRIGGERED_ASSESSMENT", 
    "SUPPLIER_AUDIT", "PRODUCT_ASSESSMENT", "SHIPMENT_ASSESSMENT"
];

// Assessment statuses
const assessmentStatuses = [
    "INITIATED", "IN_PROGRESS", "PENDING_DOCUMENTATION", "UNDER_REVIEW", 
    "COMPLETED", "APPROVED", "REJECTED", "REQUIRES_MITIGATION", "ESCALATED"
];

// Risk levels
const riskLevels = ["NEGLIGIBLE", "LOW", "STANDARD", "HIGH", "CRITICAL"];

// EUDR commodities
const eudrCommodities = ["CATTLE", "COCOA", "COFFEE", "PALM_OIL", "RUBBER", "SOY", "WOOD"];

// Assessment outcomes
const assessmentOutcomes = [
    "COMPLIANT", "NON_COMPLIANT", "CONDITIONALLY_COMPLIANT", 
    "REQUIRES_MONITORING", "REQUIRES_MITIGATION", "SUSPENDED"
];

// Mitigation measures
const mitigationMeasures = [
    "ENHANCED_MONITORING", "SUPPLIER_TRAINING", "CERTIFICATION_REQUIRED", 
    "GEOLOCATION_VERIFICATION", "THIRD_PARTY_AUDIT", "SUPPLY_CHAIN_MAPPING",
    "DOCUMENTATION_UPDATE", "RISK_MITIGATION_PLAN"
];

// Assessment criteria
const assessmentCriteria = [
    "GEOLOCATION_DATA", "DEFORESTATION_RISK", "SUPPLIER_COMPLIANCE", 
    "CERTIFICATION_STATUS", "DOCUMENTATION_COMPLETENESS", "TRACEABILITY_LEVEL",
    "LEGAL_COMPLIANCE", "ENVIRONMENTAL_IMPACT"
];

/**
 * Generate an EUDR assessment status message
 * @param {Object} options - Generation options
 * @returns {Object} Generated message with key and value
 */
function generate(options = {}) {
    // Generate assessment ID (our message key)
    const assessmentId = `EUDR-ASMT-${generateRandomAlphanumeric(10).toUpperCase()}`;
    
    // Generate assessment details
    const assessmentType = getRandomElement(assessmentTypes);
    const assessmentStatus = getRandomElement(assessmentStatuses);
    const riskLevel = getRandomElement(riskLevels);
    const outcome = assessmentStatus === "COMPLETED" || assessmentStatus === "APPROVED" ? 
        getRandomElement(assessmentOutcomes) : null;
    
    // Generate dates
    const initiatedDate = new Date(Date.now() - Math.random() * 180 * 24 * 60 * 60 * 1000); // Up to 6 months ago
    const targetCompletionDate = new Date(initiatedDate.getTime() + (Math.random() * 90 + 30) * 24 * 60 * 60 * 1000); // 30-120 days
    const actualCompletionDate = assessmentStatus === "COMPLETED" || assessmentStatus === "APPROVED" ? 
        new Date(initiatedDate.getTime() + Math.random() * 90 * 24 * 60 * 60 * 1000) : null;
    
    // Generate subject information
    const subjectType = getRandomElement(["SUPPLIER", "PRODUCT", "SHIPMENT", "FACILITY"]);
    const subjectInfo = generateSubjectInfo(subjectType);
    
    // Generate assessment criteria scores
    const criteriaScores = generateCriteriaScores();
    const overallScore = calculateOverallScore(criteriaScores);
    
    // Generate findings and recommendations
    const findings = generateFindings(riskLevel, criteriaScores);
    const recommendations = generateRecommendations(riskLevel, outcome);
    
    const messageValue = {
        assessmentId: assessmentId,
        assessmentInfo: {
            assessmentNumber: `ASMT-${generateRandomNumericString(8)}`,
            type: assessmentType,
            status: assessmentStatus,
            priority: getRandomElement(["LOW", "NORMAL", "HIGH", "URGENT"]),
            initiatedDate: initiatedDate.toISOString().split('T')[0],
            targetCompletionDate: targetCompletionDate.toISOString().split('T')[0],
            actualCompletionDate: actualCompletionDate ? actualCompletionDate.toISOString().split('T')[0] : null,
            daysElapsed: Math.floor((new Date() - initiatedDate) / (24 * 60 * 60 * 1000))
        },
        subject: {
            type: subjectType,
            id: subjectInfo.id,
            name: subjectInfo.name,
            description: subjectInfo.description,
            commodity: getRandomElement(eudrCommodities),
            location: subjectInfo.location
        },
        assessmentTeam: {
            leadAssessor: {
                id: `ASSESSOR-${generateRandomAlphanumeric(6).toUpperCase()}`,
                name: generateAssessorName(),
                role: "Lead EUDR Assessor",
                certification: getRandomElement(["EUDR_CERTIFIED", "ENVIRONMENTAL_AUDITOR", "SUPPLY_CHAIN_SPECIALIST"])
            },
            teamMembers: generateTeamMembers(),
            externalAuditor: Math.random() > 0.6 ? {
                company: getRandomElement(["SGS", "Bureau Veritas", "TÜV SÜD", "Intertek", "DNV"]),
                auditorId: `EXT-${generateRandomAlphanumeric(6).toUpperCase()}`
            } : null
        },
        riskAssessment: {
            overallRiskLevel: riskLevel,
            riskScore: generateRiskScore(riskLevel),
            deforestationRisk: getRandomElement(riskLevels),
            countryRisk: generateCountryRisk(),
            supplierRisk: getRandomElement(riskLevels),
            productRisk: getRandomElement(riskLevels),
            riskFactors: generateRiskFactors(riskLevel)
        },
        assessmentCriteria: {
            scores: criteriaScores,
            overallScore: overallScore,
            passingScore: 70,
            weightedScore: calculateWeightedScore(criteriaScores),
            criticalFailures: criteriaScores.filter(c => c.score < 50).length
        },
        findings: findings,
        outcome: {
            result: outcome,
            approved: outcome === "COMPLIANT" || outcome === "CONDITIONALLY_COMPLIANT",
            conditions: outcome === "CONDITIONALLY_COMPLIANT" ? generateConditions() : [],
            validUntil: outcome && (outcome === "COMPLIANT" || outcome === "CONDITIONALLY_COMPLIANT") ? 
                new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] : null,
            nextReviewDate: new Date(Date.now() + (Math.random() * 365 + 180) * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
        },
        recommendations: recommendations,
        mitigationPlan: {
            required: riskLevel === "HIGH" || riskLevel === "CRITICAL" || outcome === "REQUIRES_MITIGATION",
            measures: riskLevel === "HIGH" || riskLevel === "CRITICAL" ? 
                generateMitigationMeasures() : [],
            timeline: generateMitigationTimeline(),
            responsibleParty: `RESP-${generateRandomAlphanumeric(6).toUpperCase()}`,
            monitoringFrequency: getRandomElement(["MONTHLY", "QUARTERLY", "SEMI_ANNUALLY", "ANNUALLY"])
        },
        compliance: {
            eudrCompliant: outcome === "COMPLIANT",
            complianceScore: overallScore,
            lastComplianceDate: actualCompletionDate,
            complianceOfficer: `OFFICER-${generateRandomAlphanumeric(6).toUpperCase()}`,
            certificationStatus: getRandomElement(["VALID", "EXPIRED", "PENDING", "SUSPENDED", "REVOKED"]),
            auditTrail: generateAuditTrail(initiatedDate, actualCompletionDate)
        },
        documentation: {
            assessmentReport: `RPT-${generateRandomAlphanumeric(10).toUpperCase()}`,
            evidenceFiles: Math.floor(Math.random() * 20) + 5, // 5-24 files
            photographicEvidence: Math.random() > 0.3, // 70% have photos
            geolocationData: Math.random() > 0.2, // 80% have geolocation
            supplierDeclarations: Math.random() > 0.15, // 85% have declarations
            certifications: Math.random() > 0.25, // 75% have certifications
            thirdPartyReports: Math.random() > 0.5 // 50% have third-party reports
        },
        alerts: generateAssessmentAlerts(assessmentStatus, riskLevel, outcome),
        metadata: {
            generatedAt: new Date().toISOString(),
            messageIndex: options.messageIndex || 0,
            dataVersion: "1.0",
            source: "EUDR_ASSESSMENT_SYSTEM",
            lastUpdated: new Date().toISOString(),
            assessmentVersion: "1.2"
        }
    };

    return {
        key: assessmentId,
        value: messageValue
    };
}

// --- Helper Functions ---

function generateSubjectInfo(subjectType) {
    const subjects = {
        "SUPPLIER": {
            id: `EUDR-SUP-${generateRandomAlphanumeric(8).toUpperCase()}`,
            name: generateSupplierName(),
            description: "EUDR-regulated commodity supplier",
            location: generateLocation()
        },
        "PRODUCT": {
            id: `EUDR-PROD-${generateRandomAlphanumeric(10).toUpperCase()}`,
            name: generateProductName(),
            description: "EUDR-regulated product assessment",
            location: generateLocation()
        },
        "SHIPMENT": {
            id: `EUDR-SH-${generateRandomAlphanumeric(10).toUpperCase()}`,
            name: `Shipment ${generateRandomNumericString(6)}`,
            description: "Inbound EUDR commodity shipment",
            location: generateLocation()
        },
        "FACILITY": {
            id: `EUDR-FAC-${generateRandomAlphanumeric(8).toUpperCase()}`,
            name: generateFacilityName(),
            description: "Production/processing facility",
            location: generateLocation()
        }
    };
    
    return subjects[subjectType] || subjects["SUPPLIER"];
}

function generateAssessorName() {
    const firstNames = ["Dr. Sarah", "Michael", "Dr. Elena", "James", "Dr. Maria", "Robert", "Dr. Anna"];
    const lastNames = ["Johnson", "Schmidt", "Rodriguez", "Chen", "Patel", "Anderson", "Williams"];
    
    return `${getRandomElement(firstNames)} ${getRandomElement(lastNames)}`;
}

function generateTeamMembers() {
    const memberCount = Math.floor(Math.random() * 4) + 1; // 1-4 members
    const members = [];
    
    for (let i = 0; i < memberCount; i++) {
        members.push({
            id: `TEAM-${generateRandomAlphanumeric(6).toUpperCase()}`,
            name: generateAssessorName(),
            role: getRandomElement(["Environmental Specialist", "Supply Chain Analyst", "Compliance Officer", "Risk Assessor"]),
            expertise: getRandomElement(["Forestry", "Agriculture", "Supply Chain", "Environmental Law", "Risk Management"])
        });
    }
    
    return members;
}

function generateCriteriaScores() {
    return assessmentCriteria.map(criterion => ({
        criterion: criterion,
        score: Math.floor(Math.random() * 100),
        weight: getRandomFloat(0.1, 0.2),
        status: Math.random() > 0.2 ? "PASS" : "FAIL",
        comments: generateCriterionComment(criterion)
    }));
}

function calculateOverallScore(criteriaScores) {
    const totalScore = criteriaScores.reduce((sum, c) => sum + c.score, 0);
    return Math.round(totalScore / criteriaScores.length);
}

function calculateWeightedScore(criteriaScores) {
    const weightedSum = criteriaScores.reduce((sum, c) => sum + (c.score * c.weight), 0);
    const totalWeight = criteriaScores.reduce((sum, c) => sum + c.weight, 0);
    return Math.round(weightedSum / totalWeight);
}

function generateRiskScore(riskLevel) {
    const baseScores = { "NEGLIGIBLE": 10, "LOW": 30, "STANDARD": 50, "HIGH": 75, "CRITICAL": 90 };
    const base = baseScores[riskLevel] || 50;
    return Math.max(0, Math.min(100, base + (Math.random() * 20 - 10)));
}

function generateCountryRisk() {
    const countries = [
        { code: "BR", risk: "HIGH" },
        { code: "ID", risk: "HIGH" },
        { code: "MY", risk: "STANDARD" },
        { code: "CO", risk: "STANDARD" },
        { code: "GH", risk: "STANDARD" },
        { code: "VN", risk: "LOW" },
        { code: "TH", risk: "LOW" }
    ];
    
    const country = getRandomElement(countries);
    return {
        country: country.code,
        riskLevel: country.risk,
        deforestationRate: getRandomFloat(0.1, 5.0),
        governanceScore: Math.floor(Math.random() * 100)
    };
}

function generateRiskFactors(riskLevel) {
    const allFactors = [
        "High deforestation rate in origin region",
        "Limited supplier transparency",
        "Insufficient geolocation data",
        "Missing certifications",
        "Complex supply chain",
        "Weak governance in origin country",
        "Recent deforestation alerts",
        "Inadequate documentation"
    ];
    
    const factorCount = riskLevel === "CRITICAL" ? 6 : 
                      riskLevel === "HIGH" ? 4 : 
                      riskLevel === "STANDARD" ? 2 : 1;
    
    const selectedFactors = [];
    for (let i = 0; i < factorCount; i++) {
        const factor = getRandomElement(allFactors);
        if (!selectedFactors.includes(factor)) {
            selectedFactors.push(factor);
        }
    }
    
    return selectedFactors;
}

function generateFindings(riskLevel, criteriaScores) {
    const findings = [];
    
    // Add findings based on failed criteria
    criteriaScores.filter(c => c.status === "FAIL").forEach(criterion => {
        findings.push({
            type: "NON_COMPLIANCE",
            severity: criterion.score < 30 ? "HIGH" : "MEDIUM",
            description: `${criterion.criterion.replace('_', ' ').toLowerCase()} requirements not met`,
            evidence: `Score: ${criterion.score}/100`,
            recommendation: `Improve ${criterion.criterion.replace('_', ' ').toLowerCase()}`
        });
    });
    
    // Add risk-based findings
    if (riskLevel === "HIGH" || riskLevel === "CRITICAL") {
        findings.push({
            type: "HIGH_RISK",
            severity: "HIGH",
            description: "High deforestation risk identified in supply chain",
            evidence: "Risk assessment indicates elevated deforestation probability",
            recommendation: "Implement enhanced monitoring and mitigation measures"
        });
    }
    
    return findings;
}

function generateRecommendations(riskLevel, outcome) {
    const recommendations = [];
    
    if (riskLevel === "HIGH" || riskLevel === "CRITICAL") {
        recommendations.push("Implement enhanced due diligence procedures");
        recommendations.push("Require additional supplier certifications");
        recommendations.push("Increase monitoring frequency");
    }
    
    if (outcome === "NON_COMPLIANT") {
        recommendations.push("Address all non-compliance issues before approval");
        recommendations.push("Provide additional documentation");
    }
    
    if (outcome === "CONDITIONALLY_COMPLIANT") {
        recommendations.push("Fulfill all specified conditions within timeline");
        recommendations.push("Submit progress reports monthly");
    }
    
    return recommendations;
}

function generateConditions() {
    const conditions = [
        "Submit geolocation data within 30 days",
        "Obtain FSC certification within 90 days",
        "Complete supplier audit within 60 days",
        "Provide updated risk assessment quarterly",
        "Implement monitoring system within 45 days"
    ];
    
    const conditionCount = Math.floor(Math.random() * 3) + 1; // 1-3 conditions
    return conditions.slice(0, conditionCount);
}

function generateMitigationMeasures() {
    const measureCount = Math.floor(Math.random() * 4) + 2; // 2-5 measures
    return mitigationMeasures.slice(0, measureCount);
}

function generateMitigationTimeline() {
    return {
        startDate: new Date().toISOString().split('T')[0],
        endDate: new Date(Date.now() + Math.random() * 180 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // Up to 6 months
        milestones: [
            { milestone: "Initial measures implemented", dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] },
            { milestone: "Progress review", dueDate: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] },
            { milestone: "Final assessment", dueDate: new Date(Date.now() + 180 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] }
        ]
    };
}

function generateAuditTrail(initiatedDate, completionDate) {
    const events = [
        { event: "Assessment Initiated", date: initiatedDate.toISOString().split('T')[0], user: "SYSTEM" },
        { event: "Documentation Requested", date: new Date(initiatedDate.getTime() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], user: "ASSESSOR" }
    ];
    
    if (completionDate) {
        events.push({
            event: "Assessment Completed",
            date: completionDate.toISOString().split('T')[0],
            user: "LEAD_ASSESSOR"
        });
    }
    
    return events;
}

function generateAssessmentAlerts(status, riskLevel, outcome) {
    const alerts = [];
    
    if (status === "PENDING_DOCUMENTATION") {
        alerts.push({
            type: "DOCUMENTATION_REQUIRED",
            message: "Assessment pending required documentation",
            severity: "MEDIUM",
            timestamp: new Date().toISOString()
        });
    }
    
    if (riskLevel === "CRITICAL") {
        alerts.push({
            type: "CRITICAL_RISK",
            message: "Critical risk level requires immediate attention",
            severity: "CRITICAL",
            timestamp: new Date().toISOString()
        });
    }
    
    if (outcome === "NON_COMPLIANT") {
        alerts.push({
            type: "NON_COMPLIANCE",
            message: "Assessment result indicates non-compliance with EUDR",
            severity: "HIGH",
            timestamp: new Date().toISOString()
        });
    }
    
    return alerts;
}

function generateSupplierName() {
    const prefixes = ["Global", "Sustainable", "Green", "Eco", "Natural"];
    const commodities = ["Timber", "Coffee", "Cocoa", "Palm", "Rubber", "Soy", "Cattle"];
    const suffixes = ["Corp", "Ltd", "Group", "Trading", "Industries"];
    
    return `${getRandomElement(prefixes)} ${getRandomElement(commodities)} ${getRandomElement(suffixes)}`;
}

function generateProductName() {
    const commodities = ["Coffee", "Cocoa", "Palm Oil", "Rubber", "Soy", "Timber", "Beef"];
    const types = ["Premium", "Organic", "Sustainable", "Certified", "Natural"];
    
    return `${getRandomElement(types)} ${getRandomElement(commodities)}`;
}

function generateFacilityName() {
    const types = ["Processing", "Manufacturing", "Production", "Distribution"];
    const locations = ["Central", "North", "South", "East", "West"];
    
    return `${getRandomElement(locations)} ${getRandomElement(types)} Facility`;
}

function generateLocation() {
    const countries = ["Brazil", "Indonesia", "Malaysia", "Colombia", "Ghana", "Vietnam", "Thailand"];
    const regions = ["Amazon", "Sumatra", "Sabah", "Andes", "Forest Zone", "Mekong Delta", "Central Plains"];
    
    return {
        country: getRandomElement(countries),
        region: getRandomElement(regions),
        coordinates: {
            lat: getRandomFloat(-30, 30),
            lng: getRandomFloat(-180, 180)
        }
    };
}

function generateCriterionComment(criterion) {
    const comments = {
        "GEOLOCATION_DATA": "Geolocation coordinates provided and verified",
        "DEFORESTATION_RISK": "Risk assessment completed using satellite data",
        "SUPPLIER_COMPLIANCE": "Supplier compliance history reviewed",
        "CERTIFICATION_STATUS": "Certification validity confirmed",
        "DOCUMENTATION_COMPLETENESS": "All required documents submitted",
        "TRACEABILITY_LEVEL": "Supply chain traceability assessed",
        "LEGAL_COMPLIANCE": "Legal requirements compliance verified",
        "ENVIRONMENTAL_IMPACT": "Environmental impact assessment completed"
    };
    
    return comments[criterion] || "Assessment completed";
}

/**
 * Get template metadata
 * @returns {Object} Template metadata
 */
function getMetadata() {
    return {
        name: "EUDR Assessment Status Generator",
        description: "Generates EUDR compliance assessment status updates with risk levels, findings, and mitigation measures",
        version: "1.0.0",
        keyField: "assessmentId",
        author: "MuleSoft EUDR Team",
        assessmentTypes: assessmentTypes,
        riskLevels: riskLevels,
        assessmentOutcomes: assessmentOutcomes
    };
}

module.exports = {
    generate,
    getMetadata
};
