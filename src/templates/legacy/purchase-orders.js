const crypto = require("crypto");

// --- Helper Functions ---
function generateRandomNumericString(length) {
    let result = "";
    const characters = "0123456789";
    for (let i = 0; i < length; i++) {
        result += characters.charAt(Math.floor(Math.random() * characters.length));
    }
    return result;
}

function generateRandomAlphanumeric(length) {
    return crypto
        .randomBytes(Math.ceil(length / 2))
        .toString("hex")
        .slice(0, length);
}

function getRandomElement(array) {
    return array[Math.floor(Math.random() * array.length)];
}

function getRandomFloat(min, max, decimals = 2) {
    return parseFloat((Math.random() * (max - min) + min).toFixed(decimals));
}

// --- Configuration & Mappings ---

const orderStatuses = ["PENDING", "CONFIRMED", "PROCESSING", "SHIPPED", "DELIVERED", "CANCELLED"];
const currencies = ["EUR", "USD", "GBP", "CHF", "SEK", "NOK", "DKK"];
const paymentTerms = ["NET30", "NET60", "NET90", "COD", "PREPAID"];
const suppliers = [
    "ACME Corp", "Global Supplies Ltd", "Premium Partners", "Quality Goods Inc", 
    "Reliable Resources", "Swift Solutions", "Top Tier Trading", "Universal Vendors"
];

const productCategories = [
    "Electronics", "Office Supplies", "Industrial Equipment", "Raw Materials",
    "Packaging", "Tools", "Safety Equipment", "Maintenance Supplies"
];

/**
 * Generate a purchase order message
 * @param {Object} options - Generation options
 * @returns {Object} Generated message with key and value
 */
function generate(options = {}) {
    // Generate order ID (our message key)
    const orderId = `PO-${generateRandomNumericString(8)}`;
    
    // Generate order details
    const orderDate = new Date();
    const deliveryDate = new Date(orderDate.getTime() + (Math.random() * 30 + 7) * 24 * 60 * 60 * 1000); // 7-37 days from now
    
    const currency = getRandomElement(currencies);
    const itemCount = Math.floor(Math.random() * 5) + 1; // 1-5 items
    const items = [];
    let totalAmount = 0;
    
    // Generate order items
    for (let i = 0; i < itemCount; i++) {
        const quantity = Math.floor(Math.random() * 100) + 1;
        const unitPrice = getRandomFloat(10, 1000);
        const lineTotal = quantity * unitPrice;
        totalAmount += lineTotal;
        
        items.push({
            lineNumber: i + 1,
            productId: `PROD-${generateRandomAlphanumeric(6).toUpperCase()}`,
            productName: `${getRandomElement(productCategories)} Item ${generateRandomAlphanumeric(3).toUpperCase()}`,
            category: getRandomElement(productCategories),
            quantity: quantity,
            unitPrice: unitPrice,
            currency: currency,
            lineTotal: parseFloat(lineTotal.toFixed(2))
        });
    }
    
    // Calculate tax and final total
    const taxRate = 0.20; // 20% VAT
    const taxAmount = parseFloat((totalAmount * taxRate).toFixed(2));
    const finalTotal = parseFloat((totalAmount + taxAmount).toFixed(2));
    
    const messageValue = {
        orderId: orderId,
        orderNumber: `ORD-${generateRandomNumericString(6)}`,
        status: getRandomElement(orderStatuses),
        orderDate: orderDate.toISOString().split('T')[0],
        expectedDeliveryDate: deliveryDate.toISOString().split('T')[0],
        supplier: {
            name: getRandomElement(suppliers),
            supplierId: `SUP-${generateRandomNumericString(6)}`,
            contactEmail: `contact@${generateRandomAlphanumeric(8)}.com`
        },
        buyer: {
            department: getRandomElement(["Procurement", "Operations", "IT", "Facilities", "HR"]),
            buyerId: `BUY-${generateRandomNumericString(4)}`,
            approver: `${generateRandomAlphanumeric(6).charAt(0).toUpperCase()}${generateRandomAlphanumeric(5)} ${generateRandomAlphanumeric(7).charAt(0).toUpperCase()}${generateRandomAlphanumeric(6)}`
        },
        items: items,
        financial: {
            currency: currency,
            subtotal: parseFloat(totalAmount.toFixed(2)),
            taxRate: taxRate,
            taxAmount: taxAmount,
            totalAmount: finalTotal,
            paymentTerms: getRandomElement(paymentTerms)
        },
        shipping: {
            method: getRandomElement(["Standard", "Express", "Overnight", "Ground"]),
            address: {
                street: `${Math.floor(Math.random() * 9999) + 1} ${getRandomElement(["Main", "Oak", "Pine", "Elm", "Maple"])} St`,
                city: getRandomElement(["Amsterdam", "Berlin", "Paris", "London", "Madrid", "Rome"]),
                postalCode: generateRandomNumericString(5),
                country: getRandomElement(["NL", "DE", "FR", "GB", "ES", "IT"])
            }
        },
        metadata: {
            generatedAt: new Date().toISOString(),
            messageIndex: options.messageIndex || 0,
            priority: getRandomElement(["LOW", "NORMAL", "HIGH", "URGENT"])
        }
    };

    return {
        key: orderId,
        value: messageValue
    };
}

/**
 * Get template metadata
 * @returns {Object} Template metadata
 */
function getMetadata() {
    return {
        name: "Purchase Order Generator",
        description: "Generates realistic purchase order test data with multiple line items, financial calculations, and shipping details",
        version: "1.0.0",
        keyField: "orderId",
        author: "MuleSoft Team",
        supportedStatuses: orderStatuses,
        supportedCurrencies: currencies
    };
}

module.exports = {
    generate,
    getMetadata
};
