const crypto = require("crypto");

// --- Helper Functions ---
function generateRandomNumericString(length) {
    let result = "";
    const characters = "0123456789";
    for (let i = 0; i < length; i++) {
        result += characters.charAt(Math.floor(Math.random() * characters.length));
    }
    return result;
}

function generateRandomAlphanumeric(length) {
    return crypto
        .randomBytes(Math.ceil(length / 2))
        .toString("hex")
        .slice(0, length);
}

function getRandomElement(array) {
    return array[Math.floor(Math.random() * array.length)];
}

function getRandomFloat(min, max, decimals = 2) {
    return parseFloat((Math.random() * (max - min) + min).toFixed(decimals));
}

// --- EUDR Product Configuration ---

// EUDR-regulated commodities with their CN codes
const eudrCommodities = {
    "CATTLE": {
        name: "Cattle",
        cnCodes: ["0102", "0201", "0202", "1502"],
        products: ["Live Cattle", "Fresh Beef", "Frozen Beef", "Beef Tallow", "Leather"]
    },
    "COCOA": {
        name: "Cocoa",
        cnCodes: ["1801", "1803", "1804", "1805", "1806"],
        products: ["Cocoa Beans", "Cocoa Paste", "Cocoa Butter", "Cocoa Powder", "Chocolate"]
    },
    "COFFEE": {
        name: "Coffee",
        cnCodes: ["0901"],
        products: ["Green Coffee Beans", "Roasted Coffee", "Instant Coffee", "Coffee Extract"]
    },
    "PALM_OIL": {
        name: "Palm Oil",
        cnCodes: ["1511", "1513"],
        products: ["Crude Palm Oil", "Refined Palm Oil", "Palm Kernel Oil", "Palm Stearin"]
    },
    "RUBBER": {
        name: "Rubber",
        cnCodes: ["4001"],
        products: ["Natural Rubber", "Latex", "Rubber Sheets", "Rubber Blocks"]
    },
    "SOY": {
        name: "Soy",
        cnCodes: ["1201", "1507", "2304"],
        products: ["Soybeans", "Soy Oil", "Soy Meal", "Soy Protein"]
    },
    "WOOD": {
        name: "Wood",
        cnCodes: ["4403", "4407", "4409", "4412"],
        products: ["Logs", "Sawn Wood", "Plywood", "Particle Board", "Furniture"]
    }
};

// Processing levels
const processingLevels = ["RAW", "SEMI_PROCESSED", "PROCESSED", "FINISHED_PRODUCT"];

// Sustainability certifications
const certifications = ["FSC", "PEFC", "RTRS", "RSPO", "UTZ", "RAINFOREST_ALLIANCE", "FAIRTRADE", "ORGANIC"];

// Risk categories
const riskCategories = ["VERY_LOW", "LOW", "MEDIUM", "HIGH", "CRITICAL"];

// Origin countries with risk profiles
const originCountries = [
    { code: "BR", name: "Brazil", riskLevel: "HIGH" },
    { code: "ID", name: "Indonesia", riskLevel: "HIGH" },
    { code: "MY", name: "Malaysia", riskLevel: "HIGH" },
    { code: "CO", name: "Colombia", riskLevel: "MEDIUM" },
    { code: "PE", name: "Peru", riskLevel: "MEDIUM" },
    { code: "GH", name: "Ghana", riskLevel: "MEDIUM" },
    { code: "CI", name: "Côte d'Ivoire", riskLevel: "MEDIUM" },
    { code: "VN", name: "Vietnam", riskLevel: "MEDIUM" },
    { code: "TH", name: "Thailand", riskLevel: "LOW" }
];

/**
 * Generate an EUDR product message
 * @param {Object} options - Generation options
 * @returns {Object} Generated message with key and value
 */
function generate(options = {}) {
    // Generate product ID (our message key)
    const productId = `EUDR-PROD-${generateRandomAlphanumeric(10).toUpperCase()}`;
    
    // Select commodity and related product
    const commodityKey = getRandomElement(Object.keys(eudrCommodities));
    const commodity = eudrCommodities[commodityKey];
    const productName = getRandomElement(commodity.products);
    const cnCode = getRandomElement(commodity.cnCodes);
    
    // Generate product details
    const processingLevel = getRandomElement(processingLevels);
    const originCountry = getRandomElement(originCountries);
    
    // Generate batch/lot information
    const batchNumber = `BATCH-${generateRandomAlphanumeric(8).toUpperCase()}`;
    const lotNumber = `LOT-${generateRandomNumericString(6)}`;
    
    // Generate production date (within last 2 years)
    const productionDate = new Date(Date.now() - Math.random() * 730 * 24 * 60 * 60 * 1000);
    const expiryDate = new Date(productionDate.getTime() + (Math.random() * 1095 + 365) * 24 * 60 * 60 * 1000); // 1-3 years shelf life
    
    // Generate certifications
    const productCertifications = [];
    const certCount = Math.floor(Math.random() * 3); // 0-2 certifications
    for (let i = 0; i < certCount; i++) {
        const cert = getRandomElement(certifications);
        if (!productCertifications.includes(cert)) {
            productCertifications.push(cert);
        }
    }
    
    // Generate geolocation data for origin
    const originCoordinates = generateOriginCoordinates(originCountry.code);
    
    // Generate supply chain information
    const supplyChainSteps = generateSupplyChain(commodityKey, processingLevel);
    
    // Generate risk assessment
    const riskScore = generateRiskScore(originCountry.riskLevel, processingLevel);
    const riskCategory = determineRiskCategory(riskScore);
    
    const messageValue = {
        productId: productId,
        productInfo: {
            name: productName,
            description: `${processingLevel.toLowerCase().replace('_', ' ')} ${productName.toLowerCase()}`,
            commodity: commodityKey,
            commodityName: commodity.name,
            cnCode: cnCode,
            processingLevel: processingLevel,
            category: getCategoryForCommodity(commodityKey),
            subcategory: getSubcategoryForProduct(productName)
        },
        identification: {
            sku: `SKU-${generateRandomAlphanumeric(8).toUpperCase()}`,
            gtin: generateGTIN(),
            batchNumber: batchNumber,
            lotNumber: lotNumber,
            serialNumber: `SN-${generateRandomAlphanumeric(12).toUpperCase()}`
        },
        origin: {
            country: originCountry.code,
            countryName: originCountry.name,
            region: generateRegionForCountry(originCountry.code),
            coordinates: originCoordinates,
            harvestArea: {
                name: `${generateRegionForCountry(originCountry.code)} Production Area`,
                coordinates: originCoordinates,
                areaSize: getRandomFloat(10, 10000), // hectares
                landUseType: getLandUseType(commodityKey)
            }
        },
        production: {
            productionDate: productionDate.toISOString().split('T')[0],
            expiryDate: expiryDate.toISOString().split('T')[0],
            producerId: `PROD-${generateRandomAlphanumeric(8).toUpperCase()}`,
            facilityId: `FAC-${generateRandomAlphanumeric(6).toUpperCase()}`,
            productionMethod: getProductionMethod(commodityKey),
            quantity: {
                value: getRandomFloat(100, 50000),
                unit: getUnitForCommodity(commodityKey)
            }
        },
        eudrCompliance: {
            riskAssessment: {
                overallRisk: riskCategory,
                riskScore: riskScore,
                deforestationRisk: riskCategory,
                assessmentDate: new Date().toISOString().split('T')[0],
                assessedBy: `ASSESSOR-${generateRandomAlphanumeric(6).toUpperCase()}`
            },
            dueDigence: {
                completed: Math.random() > 0.1, // 90% completed
                completionDate: new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                documentationComplete: Math.random() > 0.15, // 85% complete
                traceabilityLevel: getRandomElement(["FULL", "PARTIAL", "LIMITED"])
            },
            certifications: productCertifications.map(cert => ({
                scheme: cert,
                certificateNumber: `${cert}-${generateRandomAlphanumeric(10).toUpperCase()}`,
                issueDate: new Date(productionDate.getTime() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                expiryDate: new Date(productionDate.getTime() + Math.random() * 1095 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                isValid: Math.random() > 0.05 // 95% valid
            })),
            geolocationData: {
                available: Math.random() > 0.2, // 80% have geolocation
                coordinates: originCoordinates,
                accuracy: getRandomFloat(1, 100), // meters
                collectionMethod: getRandomElement(["GPS", "SATELLITE", "SURVEY", "ESTIMATED"])
            }
        },
        supplyChain: {
            steps: supplyChainSteps,
            totalSteps: supplyChainSteps.length,
            transparency: getRandomElement(["HIGH", "MEDIUM", "LOW"]),
            verified: Math.random() > 0.3 // 70% verified
        },
        qualityMetrics: {
            grade: getRandomElement(["A", "B", "C"]),
            moistureContent: getRandomFloat(5, 15), // percentage
            purity: getRandomFloat(85, 99.5), // percentage
            defectRate: getRandomFloat(0, 5), // percentage
            testDate: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
        },
        packaging: {
            type: getPackagingType(commodityKey),
            material: getPackagingMaterial(commodityKey),
            weight: getRandomFloat(1, 1000), // kg
            dimensions: {
                length: getRandomFloat(10, 200), // cm
                width: getRandomFloat(10, 200),
                height: getRandomFloat(10, 200)
            },
            recyclable: Math.random() > 0.3 // 70% recyclable
        },
        metadata: {
            generatedAt: new Date().toISOString(),
            messageIndex: options.messageIndex || 0,
            dataVersion: "1.0",
            source: "EUDR_PRODUCT_REGISTRY",
            lastUpdated: new Date().toISOString()
        }
    };

    return {
        key: productId,
        value: messageValue
    };
}

// --- Helper Functions ---

function generateOriginCoordinates(countryCode) {
    const countryCoords = {
        "BR": { lat: getRandomFloat(-33.75, 5.27), lng: getRandomFloat(-73.99, -34.79) },
        "ID": { lat: getRandomFloat(-10.36, 5.90), lng: getRandomFloat(95.01, 141.03) },
        "MY": { lat: getRandomFloat(0.85, 7.36), lng: getRandomFloat(99.64, 119.27) },
        "CO": { lat: getRandomFloat(-4.23, 12.44), lng: getRandomFloat(-81.73, -66.87) },
        "PE": { lat: getRandomFloat(-18.35, -0.04), lng: getRandomFloat(-81.33, -68.65) },
        "GH": { lat: getRandomFloat(4.74, 11.17), lng: getRandomFloat(-3.26, 1.19) },
        "CI": { lat: getRandomFloat(4.36, 10.74), lng: getRandomFloat(-8.60, -2.49) },
        "VN": { lat: getRandomFloat(8.18, 23.39), lng: getRandomFloat(102.14, 109.46) },
        "TH": { lat: getRandomFloat(5.61, 20.46), lng: getRandomFloat(97.34, 105.64) }
    };
    
    return countryCoords[countryCode] || { lat: 0, lng: 0 };
}

function generateRiskScore(countryRisk, processingLevel) {
    const baseScores = { "LOW": 25, "MEDIUM": 55, "HIGH": 85 };
    const processingModifier = { "RAW": 10, "SEMI_PROCESSED": 5, "PROCESSED": 0, "FINISHED_PRODUCT": -5 };
    
    const base = baseScores[countryRisk] || 50;
    const modifier = processingModifier[processingLevel] || 0;
    
    return Math.max(0, Math.min(100, base + modifier + (Math.random() * 20 - 10)));
}

function determineRiskCategory(score) {
    if (score >= 80) return "CRITICAL";
    if (score >= 65) return "HIGH";
    if (score >= 45) return "MEDIUM";
    if (score >= 25) return "LOW";
    return "VERY_LOW";
}

function generateSupplyChain(commodity, processingLevel) {
    const steps = [
        { step: 1, actor: "Primary Producer", location: "Origin Country", activity: "Harvesting/Production" },
        { step: 2, actor: "Local Collector", location: "Origin Country", activity: "Collection/Aggregation" }
    ];
    
    if (processingLevel !== "RAW") {
        steps.push({ step: 3, actor: "Processor", location: "Processing Country", activity: "Processing" });
    }
    
    if (processingLevel === "FINISHED_PRODUCT") {
        steps.push({ step: 4, actor: "Manufacturer", location: "Manufacturing Country", activity: "Manufacturing" });
    }
    
    steps.push({ step: steps.length + 1, actor: "Trader/Exporter", location: "Export Country", activity: "Trading/Export" });
    
    return steps;
}

function generateGTIN() {
    // Generate a valid GTIN-13 (EAN-13)
    const prefix = "123"; // Company prefix
    const item = generateRandomNumericString(9);
    const partial = prefix + item;
    
    // Calculate check digit
    let sum = 0;
    for (let i = 0; i < partial.length; i++) {
        sum += parseInt(partial[i]) * (i % 2 === 0 ? 1 : 3);
    }
    const checkDigit = (10 - (sum % 10)) % 10;
    
    return partial + checkDigit;
}

function getCategoryForCommodity(commodity) {
    const categories = {
        "CATTLE": "Livestock Products",
        "COCOA": "Agricultural Products",
        "COFFEE": "Agricultural Products",
        "PALM_OIL": "Vegetable Oils",
        "RUBBER": "Natural Materials",
        "SOY": "Agricultural Products",
        "WOOD": "Forest Products"
    };
    
    return categories[commodity] || "Unknown";
}

function getSubcategoryForProduct(productName) {
    if (productName.includes("Oil")) return "Oils & Fats";
    if (productName.includes("Beans") || productName.includes("Seeds")) return "Seeds & Beans";
    if (productName.includes("Wood") || productName.includes("Logs")) return "Timber";
    if (productName.includes("Beef") || productName.includes("Cattle")) return "Meat Products";
    if (productName.includes("Chocolate") || productName.includes("Cocoa")) return "Cocoa Products";
    if (productName.includes("Coffee")) return "Coffee Products";
    if (productName.includes("Rubber")) return "Rubber Products";
    
    return "Other";
}

function getUnitForCommodity(commodity) {
    const units = {
        "CATTLE": "heads",
        "COCOA": "tonnes",
        "COFFEE": "tonnes",
        "PALM_OIL": "tonnes",
        "RUBBER": "tonnes",
        "SOY": "tonnes",
        "WOOD": "cubic_meters"
    };
    
    return units[commodity] || "units";
}

function getLandUseType(commodity) {
    const landUse = {
        "CATTLE": "Pasture",
        "COCOA": "Plantation",
        "COFFEE": "Plantation",
        "PALM_OIL": "Plantation",
        "RUBBER": "Plantation",
        "SOY": "Agricultural Land",
        "WOOD": "Forest"
    };
    
    return landUse[commodity] || "Agricultural Land";
}

function getProductionMethod(commodity) {
    const methods = {
        "CATTLE": getRandomElement(["Extensive Grazing", "Intensive Farming", "Semi-Intensive"]),
        "COCOA": getRandomElement(["Organic", "Conventional", "Agroforestry"]),
        "COFFEE": getRandomElement(["Shade-Grown", "Sun-Grown", "Organic"]),
        "PALM_OIL": getRandomElement(["Conventional", "Sustainable", "Organic"]),
        "RUBBER": getRandomElement(["Tapping", "Plantation", "Smallholder"]),
        "SOY": getRandomElement(["Conventional", "No-Till", "Organic"]),
        "WOOD": getRandomElement(["Selective Logging", "Clear-Cut", "Sustainable Forestry"])
    };
    
    return methods[commodity] || "Conventional";
}

function getPackagingType(commodity) {
    const packaging = {
        "CATTLE": "Refrigerated Container",
        "COCOA": "Jute Bags",
        "COFFEE": "Jute Bags",
        "PALM_OIL": "Bulk Tank",
        "RUBBER": "Bales",
        "SOY": "Bulk Container",
        "WOOD": "Bundle"
    };
    
    return packaging[commodity] || "Container";
}

function getPackagingMaterial(commodity) {
    const materials = {
        "CATTLE": "Insulated Container",
        "COCOA": "Natural Fiber",
        "COFFEE": "Natural Fiber",
        "PALM_OIL": "Steel Tank",
        "RUBBER": "Plastic Wrap",
        "SOY": "Steel Container",
        "WOOD": "Metal Strapping"
    };
    
    return materials[commodity] || "Mixed Materials";
}

function generateRegionForCountry(countryCode) {
    const regions = {
        "BR": getRandomElement(["Amazon", "Cerrado", "Atlantic Forest", "Pantanal"]),
        "ID": getRandomElement(["Sumatra", "Java", "Kalimantan", "Sulawesi"]),
        "MY": getRandomElement(["Peninsular Malaysia", "Sabah", "Sarawak"]),
        "CO": getRandomElement(["Amazon", "Andes", "Caribbean", "Pacific"]),
        "PE": getRandomElement(["Amazon", "Andes", "Coast"]),
        "GH": getRandomElement(["Northern", "Middle Belt", "Forest Zone"]),
        "CI": getRandomElement(["Forest Zone", "Savanna", "Coastal"]),
        "VN": getRandomElement(["North", "Central", "South"]),
        "TH": getRandomElement(["North", "Northeast", "Central", "South"])
    };
    
    return regions[countryCode] || "Unknown Region";
}

/**
 * Get template metadata
 * @returns {Object} Template metadata
 */
function getMetadata() {
    return {
        name: "EUDR Products Generator",
        description: "Generates EUDR-regulated product data with commodity classifications, risk assessments, and supply chain traceability",
        version: "1.0.0",
        keyField: "productId",
        author: "MuleSoft EUDR Team",
        supportedCommodities: Object.keys(eudrCommodities),
        processingLevels: processingLevels,
        riskCategories: riskCategories
    };
}

module.exports = {
    generate,
    getMetadata
};
