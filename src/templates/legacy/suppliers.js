const crypto = require("crypto");

// --- Helper Functions ---
function generateRandomNumericString(length) {
    let result = "";
    const characters = "0123456789";
    for (let i = 0; i < length; i++) {
        result += characters.charAt(Math.floor(Math.random() * characters.length));
    }
    return result;
}

function generateRandomAlphanumeric(length) {
    return crypto
        .randomBytes(Math.ceil(length / 2))
        .toString("hex")
        .slice(0, length);
}

// --- Configuration & Mappings ---

// Mapping from domain to the required suffix for businessPartnerId
const domainSuffixMap = {
    ades: "_aes",
    albert: "_acz",
    albertheijn: "_ah",
    alfabeta: "_ab",
    delhaize: "_dll",
    etos: "_eto",
    megaimage: "_mi",
    profi: "_pro",
    eurelec: "_eul",
};

// Get a list of all possible domains from the map keys
const domains = Object.keys(domainSuffixMap);

// Country codes for varied test data
const countryCodes = ["AT", "DE", "NL", "BE", "RO", "CZ", "GR"];

/**
 * Generate a supplier/business partner message
 * @param {Object} options - Generation options
 * @returns {Object} Generated message with key and value
 */
function generate(options = {}) {
    // 1. Select a random domain
    const randomDomain = domains[Math.floor(Math.random() * domains.length)];

    // 2. Generate the businessPartnerId, which will be our Kafka message key
    const randomIdPart = generateRandomNumericString(10);
    const suffix = domainSuffixMap[randomDomain];
    const businessPartnerId = `${randomIdPart}${suffix}`;

    // 3. Randomize other fields for realistic test data
    const randomCountryCode = countryCodes[Math.floor(Math.random() * countryCodes.length)];
    const eudrRelevance = true;
    const partnerTypeCode = 1;
    const relationshipTypeCode = 1;
    const randomCompanyName = `Test Corp ${generateRandomAlphanumeric(4).toUpperCase()}`;

    // 4. Construct the message value
    const messageValue = {
        domain: randomDomain,
        businessPartnerId: businessPartnerId,
        name: `${randomCompanyName} (test)`,
        eudrRelevanceIndicator: eudrRelevance,
        businessPartnerTypeCode: partnerTypeCode,
        businessRelationshipTypeCode: relationshipTypeCode,
        address: {
            countryCode: randomCountryCode,
        },
        identifiers: {
            vatId: `${randomCountryCode}${generateRandomAlphanumeric(9).toUpperCase()}`,
            glnId: generateRandomNumericString(13),
        },
        // Add timestamp for tracking
        generatedAt: new Date().toISOString(),
        messageIndex: options.messageIndex || 0
    };

    return {
        key: businessPartnerId,
        value: messageValue
    };
}

/**
 * Get template metadata
 * @returns {Object} Template metadata
 */
function getMetadata() {
    return {
        name: "Supplier Data Generator",
        description: "Generates realistic supplier/business partner test data with various domains and country codes",
        version: "1.0.0",
        keyField: "businessPartnerId",
        author: "MuleSoft Team",
        domains: domains,
        countryCodes: countryCodes
    };
}

module.exports = {
    generate,
    getMetadata
};
