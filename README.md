# 🚀 Kafka Data Producer Tool

A powerful, configurable Kafka producer tool designed specifically for **MuleSoft Developers and Architects** to generate and push realistic test data to Kafka topics across different environments.

## ✨ Features

- 🎯 **Interactive CLI** with guided prompts and colorized output
- 🔧 **EUDR Scenarios** - EU Deforestation Regulation compliant data generation with extensible template system
- 🌍 **Multi-Environment Support** - Dev, Test, and Production configurations
- 📊 **Rich Data Generation** - Realistic test data with proper relationships and constraints
- 🎨 **Beautiful Terminal UI** - Progress indicators, colored output, and technical jargon
- 🔄 **Cross-Platform** - Works on macOS, Linux, and Windows
- 📈 **Message Analytics** - Size reporting, statistics, and payload inspection
- 🧩 **Extensible** - Easy to add new data scenarios and templates

## 🏗️ Architecture

```
kafka-data-producer/
├── src/
│   ├── cli/                 # Interactive and batch CLI interfaces
│   ├── config/              # Configuration management
│   ├── templates/           # Data generation templates
│   └── utils/               # Core utilities (template engine, Kafka producer)
├── config/
│   ├── environments/        # Environment-specific Kafka configurations
│   └── scenarios/           # Scenario definitions and mappings
├── scripts/                 # Installation and setup scripts
└── docs/                    # Additional documentation
```

## 🚀 Quick Start

### Prerequisites

- **Node.js 14+** - JavaScript runtime
- **kcat (kafkacat)** - Kafka command-line tool
- **Kafka Cluster Access** - Connection details and credentials

### Installation

Choose your platform:

#### macOS
```bash
# Run the automated installer
./scripts/install-macos.sh

# Or install manually
brew install node kcat
npm install
```

#### Linux (Ubuntu/Debian)
```bash
# Run the automated installer
./scripts/install-linux.sh

# Or install manually
sudo apt-get install nodejs npm kafkacat
npm install
```

#### Windows
```powershell
# Run the automated installer (PowerShell as Administrator)
.\scripts\install-windows.ps1

# Or install manually using Chocolatey
choco install nodejs kafkacat
npm install
```

### Configuration

1. **Configure Kafka Environments**
   
   Edit the configuration files in `config/environments/`:
   
   ```bash
   # Development environment
   config/environments/dev.conf
   
   # Test environment  
   config/environments/test.conf
   
   # Production environment
   config/environments/prod.conf
   ```

2. **Verify Setup**
   
   ```bash
   npm run setup
   ```

## 🎯 Usage

### Interactive Mode (Recommended)

Launch the interactive CLI for a guided experience:

```bash
npm start
# or
node src/index.js
# or (if installed globally)
kafka-producer
```

The interactive mode will guide you through:
- 🎲 Selecting a data scenario
- 🌍 Choosing target environment
- 📊 Setting message count
- 👀 Configuring output verbosity

### Batch Mode

For automation and scripting:

```bash
# Basic usage
kafka-producer batch --scenario suppliers --count 100

# With environment and verbose output
kafka-producer batch \
  --scenario purchase-orders \
  --environment test \
  --count 50 \
  --verbose

# All available options
kafka-producer batch \
  --scenario customers \
  --environment prod \
  --count 1000 \
  --verbose
```

### List Available Scenarios

```bash
kafka-producer list-scenarios
```

## 📋 Available Scenarios

### Active Scenarios
| Scenario | Description | Topic | Key Field |
|----------|-------------|-------|-----------|
| `eudr-suppliers` | EUDR-compliant supplier data with deforestation risk assessments | `eudr.suppliers.main.v1` | `supplierId` |

### Ready-to-Activate Scenarios
Additional EUDR scenarios are pre-built and ready for activation when needed:
- **EUDR Products** - Generate EUDR-regulated product data with commodity classifications
- **EUDR Purchase Orders** - Generate purchase orders for EUDR-regulated commodities
- **EUDR Inbound Shipments** - Generate inbound shipment data with geolocation and traceability
- **EUDR Outbound Shipments** - Generate outbound shipment data with compliance documentation
- **EUDR Assessment Status** - Generate EUDR compliance assessment status updates

> 📖 **Extension Guide**: See [`docs/EUDR_EXTENSION_GUIDE.md`](docs/EUDR_EXTENSION_GUIDE.md) for detailed instructions on activating additional scenarios or creating custom templates.

## 🔧 Configuration

### Adding New Scenarios

1. **Create a Template File**
   
   Create a new JavaScript file in `src/templates/`:
   
   ```javascript
   // src/templates/my-scenario.js
   
   function generate(options = {}) {
     return {
       key: "unique-key",
       value: {
         // Your data structure here
         generatedAt: new Date().toISOString(),
         messageIndex: options.messageIndex || 0
       }
     };
   }
   
   function getMetadata() {
     return {
       name: "My Scenario",
       description: "Description of what this generates",
       version: "1.0.0",
       keyField: "uniqueKey",
       author: "Your Name"
     };
   }
   
   module.exports = { generate, getMetadata };
   ```

2. **Register the Scenario**
   
   Add your scenario to `config/scenarios/scenarios.json`:
   
   ```json
   {
     "scenarios": {
       "my-scenario": {
         "name": "My Scenario",
         "description": "Description of the scenario",
         "template": "my-scenario.js",
         "topic": "your.kafka.topic.v1",
         "keyField": "uniqueKey"
       }
     }
   }
   ```

### Environment Configuration

Each environment requires a kcat configuration file in `config/environments/`:

```ini
# config/environments/dev.conf
bootstrap.servers=your-kafka-cluster:9092
sasl.username=your-api-key
sasl.password=your-api-secret
security.protocol=SASL_SSL
sasl.mechanisms=PLAIN
```

## 🎨 Output Examples

### Interactive Mode
```
🚀 Kafka Producer Tool
┌─────────────────────────────────────────────────────┐
│                                                     │
│   🚀 MuleSoft Kafka Data Producer Tool              │
│   Generate and push test data to Kafka topics      │
│   with ease                                         │
│   Version: 1.0.0                                    │
│                                                     │
└─────────────────────────────────────────────────────┘

🎯 Welcome to the Interactive Kafka Producer!

? 🎲 Which data scenario would you like to generate? 
❯ Supplier Data - Generate supplier/business partner test data
  Purchase Orders - Generate purchase order test data  
  Customer Data - Generate customer profile test data
  Inventory Updates - Generate inventory level update data
```

### Production Output
```
📊 Production Summary:
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
Scenario: Supplier Data
Description: Generate supplier/business partner test data
Environment: DEV
Target Topic: retail.osapiens.supplier.main.v1
Message Count: 100
Key Field: businessPartnerId
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

✅ Generated 100 messages
✅ Successfully sent 100 messages to Kafka

📈 Statistics:
  Total Messages: 100
  Total Payload Size: 45,230 bytes
  Average Message Size: 452 bytes
  Largest Message: 487 bytes
  Smallest Message: 423 bytes

🎉 Mission accomplished! All messages successfully delivered to Kafka.
```

## 🛠️ Troubleshooting

### Common Issues

#### 1. kcat not found
```bash
Error: kcat (kafkacat) is not installed or not available in PATH
```

**Solution:**
- **macOS:** `brew install kcat`
- **Linux:** `sudo apt-get install kafkacat` or `sudo yum install kafkacat`
- **Windows:** Use WSL or download from [kcat releases](https://github.com/edenhill/kcat/releases)

#### 2. Node.js version too old
```bash
Error: Node.js 14+ required, found v12.x.x
```

**Solution:**
- Update Node.js to version 14 or higher
- Use Node Version Manager (nvm): `nvm install 14 && nvm use 14`

#### 3. Kafka connection failed
```bash
Error: kcat failed with code 1: %3|1234567890.123|FAIL|rdkafka#producer-1| [thrd:sasl_ssl://your-cluster:9092/bootstrap]: sasl_ssl://your-cluster:9092/bootstrap: Failed to resolve 'your-cluster:9092': nodename nor servname provided, or not known
```

**Solution:**
- Verify Kafka cluster URL in environment configuration
- Check network connectivity to Kafka cluster
- Validate API credentials

#### 4. Permission denied on scripts
```bash
Permission denied: ./scripts/install-macos.sh
```

**Solution:**
```bash
chmod +x scripts/install-macos.sh
./scripts/install-macos.sh
```

#### 5. Template not found
```bash
Error: Template file not found: src/templates/my-scenario.js
```

**Solution:**
- Ensure template file exists in `src/templates/`
- Check scenario configuration in `config/scenarios/scenarios.json`
- Verify template file exports `generate` and `getMetadata` functions

### Debug Mode

Enable debug output for troubleshooting:

```bash
DEBUG=kafka-producer* npm start
```

### Logs and Monitoring

- Check Kafka cluster logs for message delivery confirmation
- Use Kafka tools to verify messages: `kcat -C -F config/environments/dev.conf -t your-topic`
- Monitor topic partitions and consumer lag

## 🔒 Security Best Practices

### Credential Management

1. **Never commit credentials** to version control
2. **Use environment variables** for sensitive data:
   ```bash
   export KAFKA_API_KEY="your-api-key"
   export KAFKA_API_SECRET="your-api-secret"
   ```
3. **Rotate credentials** regularly
4. **Use least-privilege access** - only necessary topic permissions

### Environment Separation

- **Development:** Use dedicated dev clusters and topics
- **Test:** Separate test environment with realistic data volumes
- **Production:** Strict access controls and monitoring

## 📚 Advanced Usage

### Custom Data Templates

Create sophisticated data generators with relationships:

```javascript
// src/templates/advanced-orders.js
const { faker } = require('@faker-js/faker');

function generate(options = {}) {
  const customerId = faker.datatype.uuid();
  const orderDate = faker.date.recent(30);

  // Generate related line items
  const items = Array.from({ length: faker.datatype.number({ min: 1, max: 5 }) }, (_, i) => ({
    lineNumber: i + 1,
    productId: faker.datatype.uuid(),
    quantity: faker.datatype.number({ min: 1, max: 10 }),
    unitPrice: parseFloat(faker.commerce.price())
  }));

  return {
    key: `ORDER-${faker.datatype.number({ min: 100000, max: 999999 })}`,
    value: {
      customerId,
      orderDate: orderDate.toISOString(),
      items,
      totalAmount: items.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0)
    }
  };
}
```

### Batch Processing

Process large datasets efficiently:

```bash
# Generate 10,000 messages in batches
for i in {1..10}; do
  kafka-producer batch --scenario suppliers --count 1000 --environment dev
  sleep 5  # Rate limiting
done
```

### Integration with CI/CD

```yaml
# .github/workflows/kafka-data-setup.yml
name: Setup Test Data
on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Target environment'
        required: true
        default: 'test'
        type: choice
        options:
        - dev
        - test
      scenario:
        description: 'Data scenario'
        required: true
        default: 'suppliers'
        type: choice
        options:
        - suppliers
        - customers
        - purchase-orders

jobs:
  setup-data:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - uses: actions/setup-node@v3
      with:
        node-version: '18'
    - run: npm install
    - run: sudo apt-get install kafkacat
    - run: |
        kafka-producer batch \
          --scenario ${{ github.event.inputs.scenario }} \
          --environment ${{ github.event.inputs.environment }} \
          --count 100
```

## 🤝 Contributing

### Adding New Templates

1. Fork the repository
2. Create a new template in `src/templates/`
3. Add scenario configuration
4. Test thoroughly
5. Submit a pull request

### Template Guidelines

- **Realistic Data:** Generate data that resembles production scenarios
- **Performance:** Optimize for speed when generating large volumes
- **Consistency:** Follow existing patterns and naming conventions
- **Documentation:** Include clear metadata and comments

## 📄 License

MIT License - see [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Issues:** [GitHub Issues](https://github.com/your-org/kafka-data-producer/issues)
- **Documentation:** [Wiki](https://github.com/your-org/kafka-data-producer/wiki)
- **MuleSoft Community:** [MuleSoft Forums](https://help.mulesoft.com/s/)

---

**Built with ❤️ for the MuleSoft Community**
