{"scenarios": {"eudr-suppliers": {"name": "EUDR Suppliers", "description": "Generate EUDR-compliant supplier data with deforestation risk assessments", "template": "eudr-suppliers.js", "topic": "eudr.suppliers.main.v1", "keyField": "supplierId"}}, "_extension_guide": {"comment": "To add new EUDR scenarios, follow this pattern:", "template": {"scenario-key": {"name": "Human-readable scenario name", "description": "Brief description of what this scenario generates", "template": "template-filename.js (must exist in src/templates/)", "topic": "kafka.topic.name.v1 (follow naming convention)", "keyField": "fieldName (the JSON field used as Kafka message key)"}}, "steps_to_add_scenario": ["1. Create template file in src/templates/ following the pattern of eudr-suppliers.js", "2. Template must export generate(options) and getMetadata() functions", "3. Add scenario configuration to the 'scenarios' object above", "4. Test with: node src/index.js list-scenarios", "5. Test generation with: node src/index.js (interactive mode)"], "available_templates": {"note": "These templates are already created and ready to use:", "eudr-products": "src/templates/eudr-products.js", "eudr-purchase-orders": "src/templates/eudr-purchase-orders.js", "eudr-inbound-shipments": "src/templates/eudr-inbound-shipments.js", "eudr-outbound-shipments": "src/templates/eudr-outbound-shipments.js", "eudr-assessment-status": "src/templates/eudr-assessment-status.js"}}}