const crypto = require("crypto");

// --- Helper Functions ---
function generateRandomNumericString(length) {
    let result = "";
    const characters = "0123456789";
    for (let i = 0; i < length; i++) {
        result += characters.charAt(Math.floor(Math.random() * characters.length));
    }
    return result;
}

function generateRandomAlphanumeric(length) {
    return crypto
        .randomBytes(Math.ceil(length / 2))
        .toString("hex")
        .slice(0, length);
}

function getRandomElement(array) {
    return array[Math.floor(Math.random() * array.length)];
}

function getRandomFloat(min, max, decimals = 2) {
    return parseFloat((Math.random() * (max - min) + min).toFixed(decimals));
}

// --- EUDR Configuration & Mappings ---

// EUDR-regulated commodities
const eudrCommodities = [
    "CATTLE", "COCOA", "COFFEE", "PALM_OIL", "RUBB<PERSON>", "SOY", "WOOD"
];

// Risk levels for deforestation assessment
const riskLevels = ["LOW", "MEDIUM", "HIGH", "CRITICAL"];

// Certification schemes
const certificationSchemes = [
    "FSC", "PEFC", "RTRS", "RSPO", "UTZ", "RAINFOREST_ALLIANCE", "FAIRTRADE", "ORGANIC"
];

// Countries with different deforestation risk profiles
const supplierCountries = [
    { code: "BR", name: "Brazil", riskProfile: "HIGH" },
    { code: "ID", name: "Indonesia", riskProfile: "HIGH" },
    { code: "MY", name: "Malaysia", riskProfile: "HIGH" },
    { code: "CO", name: "Colombia", riskProfile: "MEDIUM" },
    { code: "PE", name: "Peru", riskProfile: "MEDIUM" },
    { code: "GH", name: "Ghana", riskProfile: "MEDIUM" },
    { code: "CI", name: "Côte d'Ivoire", riskProfile: "MEDIUM" },
    { code: "VN", name: "Vietnam", riskProfile: "MEDIUM" },
    { code: "TH", name: "Thailand", riskProfile: "LOW" },
    { code: "NL", name: "Netherlands", riskProfile: "LOW" },
    { code: "DE", name: "Germany", riskProfile: "LOW" },
    { code: "FR", name: "France", riskProfile: "LOW" }
];

// Company types
const companyTypes = [
    "PRIMARY_PRODUCER", "PROCESSOR", "TRADER", "MANUFACTURER", "IMPORTER", "DISTRIBUTOR"
];

/**
 * Generate an EUDR supplier message
 * @param {Object} options - Generation options
 * @returns {Object} Generated message with key and value
 */
function generate(options = {}) {
    // Generate supplier ID (our message key)
    const supplierId = `EUDR-SUP-${generateRandomAlphanumeric(8).toUpperCase()}`;
    
    // Select supplier country and associated risk profile
    const supplierCountry = getRandomElement(supplierCountries);
    const primaryCommodity = getRandomElement(eudrCommodities);
    const companyType = getRandomElement(companyTypes);
    
    // Generate company information
    const companyName = `${getRandomElement([
        "Global", "International", "Premium", "Sustainable", "Green", "Eco", "Natural", "Organic"
    ])} ${primaryCommodity.toLowerCase().charAt(0).toUpperCase() + primaryCommodity.toLowerCase().slice(1)} ${getRandomElement([
        "Corp", "Ltd", "Inc", "Group", "Trading", "Industries", "Partners", "Solutions"
    ])}`;
    
    // Generate certifications based on commodity and risk profile
    const certifications = [];
    const certCount = Math.floor(Math.random() * 3) + 1; // 1-3 certifications
    for (let i = 0; i < certCount; i++) {
        const cert = getRandomElement(certificationSchemes);
        if (!certifications.includes(cert)) {
            certifications.push(cert);
        }
    }
    
    // Generate geolocation data (approximate coordinates for the country)
    const geoCoordinates = generateGeoCoordinates(supplierCountry.code);
    
    // Generate risk assessment based on country profile
    const baseRisk = supplierCountry.riskProfile;
    const riskScore = generateRiskScore(baseRisk);
    const deforestationRisk = determineRiskLevel(riskScore);
    
    // Generate due diligence information
    const lastAssessmentDate = new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000);
    const nextAssessmentDate = new Date(lastAssessmentDate.getTime() + (365 * 24 * 60 * 60 * 1000)); // 1 year later
    
    const messageValue = {
        supplierId: supplierId,
        companyInfo: {
            name: companyName,
            legalName: `${companyName} ${getRandomElement(["B.V.", "S.A.", "Ltda.", "Sdn Bhd", "GmbH", "LLC"])}`,
            registrationNumber: `REG-${generateRandomAlphanumeric(10).toUpperCase()}`,
            taxId: `TAX-${supplierCountry.code}-${generateRandomNumericString(8)}`,
            companyType: companyType,
            establishedYear: 1990 + Math.floor(Math.random() * 30)
        },
        location: {
            country: supplierCountry.code,
            countryName: supplierCountry.name,
            region: generateRegion(supplierCountry.code),
            address: {
                street: `${Math.floor(Math.random() * 999) + 1} ${getRandomElement(["Main", "Industrial", "Forest", "Plantation", "Rural"])} ${getRandomElement(["Street", "Road", "Avenue", "Way"])}`,
                city: generateCity(supplierCountry.code),
                postalCode: generateRandomNumericString(5),
                coordinates: geoCoordinates
            }
        },
        eudrCompliance: {
            primaryCommodities: [primaryCommodity],
            secondaryCommodities: generateSecondaryCommodities(primaryCommodity),
            riskAssessment: {
                overallRisk: deforestationRisk,
                riskScore: riskScore,
                countryRiskProfile: supplierCountry.riskProfile,
                lastAssessmentDate: lastAssessmentDate.toISOString().split('T')[0],
                nextAssessmentDate: nextAssessmentDate.toISOString().split('T')[0],
                assessedBy: `ASSESSOR-${generateRandomAlphanumeric(6).toUpperCase()}`
            },
            certifications: certifications.map(cert => ({
                scheme: cert,
                certificateNumber: `${cert}-${generateRandomAlphanumeric(8).toUpperCase()}`,
                issueDate: new Date(Date.now() - Math.random() * 1095 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // Up to 3 years ago
                expiryDate: new Date(Date.now() + Math.random() * 1095 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // Up to 3 years from now
                isValid: Math.random() > 0.1 // 90% valid
            })),
            traceabilityLevel: getRandomElement(["FULL", "PARTIAL", "LIMITED", "NONE"]),
            hasGeolocationData: Math.random() > 0.2, // 80% have geolocation
            supplyChainTransparency: getRandomElement(["HIGH", "MEDIUM", "LOW"])
        },
        businessMetrics: {
            annualRevenue: getRandomFloat(100000, 50000000),
            currency: getCurrencyForCountry(supplierCountry.code),
            employeeCount: Math.floor(Math.random() * 5000) + 10,
            productionCapacity: {
                value: getRandomFloat(1000, 100000),
                unit: getUnitForCommodity(primaryCommodity),
                period: "ANNUAL"
            }
        },
        contactInfo: {
            primaryContact: {
                name: generatePersonName(),
                title: getRandomElement(["CEO", "Sustainability Manager", "Supply Chain Director", "Operations Manager"]),
                email: `contact@${companyName.toLowerCase().replace(/\s+/g, '')}.com`,
                phone: `+${getCountryCode(supplierCountry.code)}${generateRandomNumericString(9)}`
            },
            sustainabilityContact: {
                name: generatePersonName(),
                title: "Sustainability Officer",
                email: `sustainability@${companyName.toLowerCase().replace(/\s+/g, '')}.com`,
                phone: `+${getCountryCode(supplierCountry.code)}${generateRandomNumericString(9)}`
            }
        },
        metadata: {
            generatedAt: new Date().toISOString(),
            messageIndex: options.messageIndex || 0,
            dataVersion: "1.0",
            source: "EUDR_SUPPLIER_REGISTRY"
        }
    };

    return {
        key: supplierId,
        value: messageValue
    };
}

// --- Helper Functions for EUDR-specific data generation ---

function generateGeoCoordinates(countryCode) {
    const countryCoords = {
        "BR": { lat: getRandomFloat(-33.75, 5.27), lng: getRandomFloat(-73.99, -34.79) },
        "ID": { lat: getRandomFloat(-10.36, 5.90), lng: getRandomFloat(95.01, 141.03) },
        "MY": { lat: getRandomFloat(0.85, 7.36), lng: getRandomFloat(99.64, 119.27) },
        "CO": { lat: getRandomFloat(-4.23, 12.44), lng: getRandomFloat(-81.73, -66.87) },
        "PE": { lat: getRandomFloat(-18.35, -0.04), lng: getRandomFloat(-81.33, -68.65) },
        "GH": { lat: getRandomFloat(4.74, 11.17), lng: getRandomFloat(-3.26, 1.19) },
        "CI": { lat: getRandomFloat(4.36, 10.74), lng: getRandomFloat(-8.60, -2.49) },
        "VN": { lat: getRandomFloat(8.18, 23.39), lng: getRandomFloat(102.14, 109.46) },
        "TH": { lat: getRandomFloat(5.61, 20.46), lng: getRandomFloat(97.34, 105.64) },
        "NL": { lat: getRandomFloat(50.75, 53.55), lng: getRandomFloat(3.31, 7.09) },
        "DE": { lat: getRandomFloat(47.27, 55.06), lng: getRandomFloat(5.87, 15.04) },
        "FR": { lat: getRandomFloat(41.33, 51.12), lng: getRandomFloat(-5.14, 9.56) }
    };
    
    return countryCoords[countryCode] || { lat: 0, lng: 0 };
}

function generateRiskScore(baseRisk) {
    const baseScores = { "LOW": 20, "MEDIUM": 50, "HIGH": 80, "CRITICAL": 95 };
    const base = baseScores[baseRisk] || 50;
    return Math.max(0, Math.min(100, base + (Math.random() * 20 - 10))); // ±10 variation
}

function determineRiskLevel(score) {
    if (score >= 80) return "HIGH";
    if (score >= 60) return "MEDIUM";
    if (score >= 40) return "LOW";
    return "VERY_LOW";
}

function generateSecondaryCommodities(primary) {
    const related = {
        "CATTLE": ["SOY", "WOOD"],
        "COCOA": ["PALM_OIL"],
        "COFFEE": ["WOOD"],
        "PALM_OIL": ["RUBBER"],
        "RUBBER": ["PALM_OIL", "WOOD"],
        "SOY": ["CATTLE", "WOOD"],
        "WOOD": ["CATTLE", "SOY", "RUBBER"]
    };
    
    const secondaries = related[primary] || [];
    return secondaries.filter(() => Math.random() > 0.7); // 30% chance for each
}

function generateRegion(countryCode) {
    const regions = {
        "BR": ["Amazon", "Cerrado", "Atlantic Forest", "Pantanal"],
        "ID": ["Sumatra", "Java", "Kalimantan", "Sulawesi"],
        "MY": ["Peninsular Malaysia", "Sabah", "Sarawak"],
        "CO": ["Amazon", "Andes", "Caribbean", "Pacific"],
        "PE": ["Amazon", "Andes", "Coast"],
        "GH": ["Northern", "Middle Belt", "Forest Zone"],
        "CI": ["Forest Zone", "Savanna", "Coastal"],
        "VN": ["North", "Central", "South"],
        "TH": ["North", "Northeast", "Central", "South"],
        "NL": ["North Holland", "South Holland", "Utrecht"],
        "DE": ["Bavaria", "North Rhine-Westphalia", "Baden-Württemberg"],
        "FR": ["Île-de-France", "Provence", "Normandy"]
    };
    
    return getRandomElement(regions[countryCode] || ["Unknown Region"]);
}

function generateCity(countryCode) {
    const cities = {
        "BR": ["São Paulo", "Rio de Janeiro", "Manaus", "Brasília"],
        "ID": ["Jakarta", "Surabaya", "Medan", "Bandung"],
        "MY": ["Kuala Lumpur", "Johor Bahru", "Penang", "Kota Kinabalu"],
        "CO": ["Bogotá", "Medellín", "Cali", "Barranquilla"],
        "PE": ["Lima", "Arequipa", "Trujillo", "Iquitos"],
        "GH": ["Accra", "Kumasi", "Tamale", "Cape Coast"],
        "CI": ["Abidjan", "Bouaké", "Daloa", "Yamoussoukro"],
        "VN": ["Ho Chi Minh City", "Hanoi", "Da Nang", "Can Tho"],
        "TH": ["Bangkok", "Chiang Mai", "Phuket", "Pattaya"],
        "NL": ["Amsterdam", "Rotterdam", "The Hague", "Utrecht"],
        "DE": ["Berlin", "Munich", "Hamburg", "Cologne"],
        "FR": ["Paris", "Lyon", "Marseille", "Toulouse"]
    };
    
    return getRandomElement(cities[countryCode] || ["Unknown City"]);
}

function getCurrencyForCountry(countryCode) {
    const currencies = {
        "BR": "BRL", "ID": "IDR", "MY": "MYR", "CO": "COP", "PE": "PEN",
        "GH": "GHS", "CI": "XOF", "VN": "VND", "TH": "THB",
        "NL": "EUR", "DE": "EUR", "FR": "EUR"
    };
    
    return currencies[countryCode] || "USD";
}

function getUnitForCommodity(commodity) {
    const units = {
        "CATTLE": "heads", "COCOA": "tonnes", "COFFEE": "tonnes",
        "PALM_OIL": "tonnes", "RUBBER": "tonnes", "SOY": "tonnes", "WOOD": "cubic_meters"
    };
    
    return units[commodity] || "units";
}

function getCountryCode(countryCode) {
    const codes = {
        "BR": "55", "ID": "62", "MY": "60", "CO": "57", "PE": "51",
        "GH": "233", "CI": "225", "VN": "84", "TH": "66",
        "NL": "31", "DE": "49", "FR": "33"
    };
    
    return codes[countryCode] || "1";
}

function generatePersonName() {
    const firstNames = ["João", "Maria", "Ahmad", "Siti", "Carlos", "Ana", "Kwame", "Ama", "Nguyen", "Somchai", "Hans", "Marie"];
    const lastNames = ["Silva", "Santos", "Rahman", "Abdullah", "García", "López", "Asante", "Osei", "Tran", "Patel", "Müller", "Dubois"];
    
    return `${getRandomElement(firstNames)} ${getRandomElement(lastNames)}`;
}

/**
 * Get template metadata
 * @returns {Object} Template metadata
 */
function getMetadata() {
    return {
        name: "EUDR Suppliers Generator",
        description: "Generates EUDR-compliant supplier data with deforestation risk assessments, certifications, and geolocation information",
        version: "1.0.0",
        keyField: "supplierId",
        author: "MuleSoft EUDR Team",
        supportedCommodities: eudrCommodities,
        riskLevels: riskLevels,
        certificationSchemes: certificationSchemes
    };
}

module.exports = {
    generate,
    getMetadata
};
