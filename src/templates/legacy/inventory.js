const crypto = require("crypto");

// --- Helper Functions ---
function generateRandomNumericString(length) {
    let result = "";
    const characters = "0123456789";
    for (let i = 0; i < length; i++) {
        result += characters.charAt(Math.floor(Math.random() * characters.length));
    }
    return result;
}

function generateRandomAlphanumeric(length) {
    return crypto
        .randomBytes(Math.ceil(length / 2))
        .toString("hex")
        .slice(0, length);
}

function getRandomElement(array) {
    return array[Math.floor(Math.random() * array.length)];
}

function getRandomFloat(min, max, decimals = 2) {
    return parseFloat((Math.random() * (max - min) + min).toFixed(decimals));
}

// --- Configuration & Mappings ---

const productCategories = [
    "Electronics", "Clothing", "Home & Garden", "Sports & Outdoors", "Books", 
    "Health & Beauty", "Automotive", "Tools & Hardware", "Food & Beverages", "Toys & Games"
];

const brands = [
    "TechCorp", "StyleMax", "HomeComfort", "SportsPro", "BookWorld", 
    "BeautyPlus", "AutoParts", "ToolMaster", "FreshFood", "PlayTime"
];

const warehouses = [
    { id: "WH001", name: "Central Warehouse", location: "Amsterdam, NL" },
    { id: "WH002", name: "North Distribution Center", location: "Hamburg, DE" },
    { id: "WH003", name: "South Fulfillment Hub", location: "Milan, IT" },
    { id: "WH004", name: "West Regional Depot", location: "Madrid, ES" },
    { id: "WH005", name: "East Storage Facility", location: "Warsaw, PL" }
];

const movementTypes = [
    "STOCK_IN", "STOCK_OUT", "TRANSFER", "ADJUSTMENT", "RETURN", 
    "DAMAGED", "EXPIRED", "RESERVED", "UNRESERVED", "CYCLE_COUNT"
];

const suppliers = [
    "Global Supplies Ltd", "Premium Partners", "Quality Goods Inc", 
    "Reliable Resources", "Swift Solutions", "Top Tier Trading"
];

/**
 * Generate an inventory update message
 * @param {Object} options - Generation options
 * @returns {Object} Generated message with key and value
 */
function generate(options = {}) {
    // Generate product ID (our message key)
    const productId = `PROD-${generateRandomAlphanumeric(8).toUpperCase()}`;
    
    // Generate product information
    const category = getRandomElement(productCategories);
    const brand = getRandomElement(brands);
    const warehouse = getRandomElement(warehouses);
    const movementType = getRandomElement(movementTypes);
    
    // Generate SKU and product details
    const sku = `${brand.substring(0, 3).toUpperCase()}-${category.substring(0, 3).toUpperCase()}-${generateRandomNumericString(6)}`;
    const productName = `${brand} ${category} Item ${generateRandomAlphanumeric(4).toUpperCase()}`;
    
    // Generate inventory quantities
    const previousQuantity = Math.floor(Math.random() * 1000);
    let quantityChange = 0;
    let newQuantity = previousQuantity;
    
    switch (movementType) {
        case "STOCK_IN":
            quantityChange = Math.floor(Math.random() * 500) + 1;
            newQuantity = previousQuantity + quantityChange;
            break;
        case "STOCK_OUT":
            quantityChange = -Math.min(Math.floor(Math.random() * 100) + 1, previousQuantity);
            newQuantity = previousQuantity + quantityChange;
            break;
        case "TRANSFER":
            quantityChange = -Math.min(Math.floor(Math.random() * 50) + 1, previousQuantity);
            newQuantity = previousQuantity + quantityChange;
            break;
        case "ADJUSTMENT":
            quantityChange = Math.floor(Math.random() * 21) - 10; // -10 to +10
            newQuantity = Math.max(0, previousQuantity + quantityChange);
            break;
        case "RETURN":
            quantityChange = Math.floor(Math.random() * 20) + 1;
            newQuantity = previousQuantity + quantityChange;
            break;
        case "DAMAGED":
        case "EXPIRED":
            quantityChange = -Math.min(Math.floor(Math.random() * 10) + 1, previousQuantity);
            newQuantity = previousQuantity + quantityChange;
            break;
        case "RESERVED":
            quantityChange = -Math.min(Math.floor(Math.random() * 50) + 1, previousQuantity);
            newQuantity = previousQuantity; // Available quantity doesn't change, but reserved does
            break;
        case "UNRESERVED":
            quantityChange = Math.floor(Math.random() * 50) + 1;
            newQuantity = previousQuantity; // Available quantity doesn't change, but reserved does
            break;
        case "CYCLE_COUNT":
            // Cycle count might reveal discrepancies
            const countedQuantity = Math.max(0, previousQuantity + (Math.floor(Math.random() * 11) - 5));
            quantityChange = countedQuantity - previousQuantity;
            newQuantity = countedQuantity;
            break;
    }
    
    // Generate pricing information
    const unitCost = getRandomFloat(5, 500);
    const unitPrice = getRandomFloat(unitCost * 1.2, unitCost * 3); // 20% to 200% markup
    
    // Generate thresholds
    const reorderPoint = Math.floor(Math.random() * 50) + 10;
    const maxStock = Math.floor(Math.random() * 500) + 200;
    
    const messageValue = {
        productId: productId,
        sku: sku,
        productInfo: {
            name: productName,
            category: category,
            brand: brand,
            description: `High-quality ${category.toLowerCase()} product from ${brand}`,
            barcode: generateRandomNumericString(13),
            weight: getRandomFloat(0.1, 50), // kg
            dimensions: {
                length: getRandomFloat(5, 100), // cm
                width: getRandomFloat(5, 100),
                height: getRandomFloat(5, 100)
            }
        },
        inventory: {
            warehouse: warehouse,
            movementType: movementType,
            previousQuantity: previousQuantity,
            quantityChange: quantityChange,
            newQuantity: Math.max(0, newQuantity),
            availableQuantity: Math.max(0, newQuantity - Math.floor(Math.random() * 20)), // Some might be reserved
            reservedQuantity: Math.floor(Math.random() * 20),
            unit: getRandomElement(["PCS", "KG", "L", "M", "BOX", "PACK"])
        },
        thresholds: {
            reorderPoint: reorderPoint,
            maxStock: maxStock,
            safetyStock: Math.floor(reorderPoint * 0.5),
            isLowStock: newQuantity <= reorderPoint,
            isOverstock: newQuantity >= maxStock
        },
        financial: {
            unitCost: unitCost,
            unitPrice: unitPrice,
            totalValue: parseFloat((newQuantity * unitCost).toFixed(2)),
            currency: "EUR",
            lastCostUpdate: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
        },
        supplier: {
            name: getRandomElement(suppliers),
            supplierId: `SUP-${generateRandomNumericString(6)}`,
            leadTimeDays: Math.floor(Math.random() * 14) + 1,
            minimumOrderQuantity: Math.floor(Math.random() * 50) + 10
        },
        tracking: {
            lastMovementDate: new Date().toISOString(),
            lastCountDate: new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            movementReason: movementType === "ADJUSTMENT" ? getRandomElement(["CYCLE_COUNT_ADJUSTMENT", "DAMAGE_WRITE_OFF", "THEFT", "SYSTEM_CORRECTION"]) : 
                           movementType === "STOCK_IN" ? getRandomElement(["PURCHASE_ORDER", "RETURN_FROM_CUSTOMER", "TRANSFER_IN", "PRODUCTION"]) :
                           movementType === "STOCK_OUT" ? getRandomElement(["SALE", "TRANSFER_OUT", "PRODUCTION_CONSUMPTION", "SAMPLE"]) : null,
            batchNumber: `BATCH-${generateRandomAlphanumeric(8).toUpperCase()}`,
            expiryDate: Math.random() > 0.7 ? new Date(Date.now() + Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] : null
        },
        metadata: {
            generatedAt: new Date().toISOString(),
            messageIndex: options.messageIndex || 0,
            systemSource: "INVENTORY_MANAGEMENT_SYSTEM",
            transactionId: `TXN-${generateRandomAlphanumeric(12).toUpperCase()}`
        }
    };

    return {
        key: productId,
        value: messageValue
    };
}

/**
 * Get template metadata
 * @returns {Object} Template metadata
 */
function getMetadata() {
    return {
        name: "Inventory Update Generator",
        description: "Generates realistic inventory movement and stock level update data with detailed product information, warehouse tracking, and financial data",
        version: "1.0.0",
        keyField: "productId",
        author: "MuleSoft Team",
        supportedMovementTypes: movementTypes,
        supportedCategories: productCategories
    };
}

module.exports = {
    generate,
    getMetadata
};
